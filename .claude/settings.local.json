{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(rm:*)", "<PERSON><PERSON>(deno task:*)", "Bash(deno check:*)", "<PERSON><PERSON>(deno run:*)", "Bash(grep:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(timeout:*)", "Bash(lsof:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(cp:*)", "<PERSON><PERSON>(mv:*)", "Bash(ls:*)", "mcp__ide__getDiagnostics", "Bash(find:*)", "Bash(deno lint:*)", "Bash(rg:*)", "Bash(deno:*)", "Bash(deployctl:*)", "Bash(ping:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(echo:*)", "WebFetch(domain:github.com)"], "deny": []}}