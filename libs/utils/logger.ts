/**
 * Standardized conditional logging and error handling utilities
 * Eliminates the 96+ repeated logging patterns across the codebase
 * 
 * Environment Variables:
 * - LOG_LEVEL: DEBUG|INFO|WARN|ERROR (default: ERROR in prod, DEBUG in dev)
 * - ENABLE_STRUCTURED_LOGS: true|false (default: false in prod, true in dev)
 * - ENABLE_CLIENT_LOGS: true|false (default: false in prod, true in dev)
 * - DEBUG: true|false (legacy support, enables debug logging)
 */

// Log levels for consistent formatting
export const LogLevel = {
  DEBUG: 'DEBUG',
  INFO: 'INFO',
  WARN: 'WARN',
  ERROR: 'ERROR'
} as const;

export type LogLevel = typeof LogLevel[keyof typeof LogLevel];

// Environment detection
const isProduction = !!Deno.env.get("DENO_DEPLOYMENT_ID") || Deno.env.get("DENO_ENV") === "production";
const isDevelopment = !isProduction;

// Logging configuration based on environment
const logConfig = {
  level: Deno.env.get("LOG_LEVEL") || (isProduction ? "ERROR" : "DEBUG"),
  enableStructured: Deno.env.get("ENABLE_STRUCTURED_LOGS") === "true" || (isDevelopment && Deno.env.get("ENABLE_STRUCTURED_LOGS") !== "false"),
  enableClientLogs: Deno.env.get("ENABLE_CLIENT_LOGS") === "true" || (isDevelopment && Deno.env.get("ENABLE_CLIENT_LOGS") !== "false"),
  enableDebug: Deno.env.get("DEBUG") === "true" || isDevelopment
};

// Log level hierarchy for filtering
const logLevelOrder = { DEBUG: 0, INFO: 1, WARN: 2, ERROR: 3 };
const minLogLevel = logLevelOrder[logConfig.level as keyof typeof logLevelOrder] ?? 1;

/**
 * Check if a log level should be output based on configuration
 */
function shouldLog(level: LogLevel): boolean {
  return logLevelOrder[level] >= minLogLevel;
}

/**
 * Create a standardized log entry with consistent formatting
 */
function createLogEntry(level: LogLevel, context: string, message: string, data?: any): string {
  const timestamp = new Date().toISOString();
  const baseLog = `[${timestamp}] ${level} [${context}] ${message}`;

  if (data) {
    return `${baseLog} | Data: ${JSON.stringify(data)}`;
  }

  return baseLog;
}

/**
 * Log game events with structured format
 * Replaces scattered console.log statements with consistent format
 */
export function logGameEvent(event: string, data: any, context: string = 'GAME'): void {
  if (!shouldLog(LogLevel.INFO)) return;

  const logEntry = createLogEntry(LogLevel.INFO, context, `Game event: ${event}`, data);
  console.log(logEntry);

  // Also create structured log for parsing (if enabled)
  if (logConfig.enableStructured) {
    const structuredLog = {
      timestamp: new Date().toISOString(),
      level: LogLevel.INFO,
      context,
      event,
      data
    };

    console.log("STRUCTURED_LOG:", JSON.stringify(structuredLog));
  }
}

/**
 * Log authentication events
 */
export function logAuthEvent(event: string, username: string, data?: any): void {
  logGameEvent(event, { username, ...data }, 'AUTH');
}

/**
 * Log API requests with consistent format
 */
export function logApiRequest(method: string, path: string, username?: string, data?: any): void {
  logGameEvent('api_request', { method, path, username, ...data }, 'API');
}

/**
 * Log errors with consistent format and context
 * Replaces scattered console.error statements
 */
export function logError(context: string, error: Error, additionalData?: any): void {
  if (!shouldLog(LogLevel.ERROR)) return;

  const errorData = {
    message: error.message,
    stack: error.stack,
    name: error.name,
    ...additionalData
  };

  const logEntry = createLogEntry(LogLevel.ERROR, context, `Error occurred: ${error.message}`, errorData);
  console.error(logEntry);

  // Structured error log for monitoring systems (if enabled)
  if (logConfig.enableStructured) {
    const structuredLog = {
      timestamp: new Date().toISOString(),
      level: LogLevel.ERROR,
      context,
      error: errorData
    };

    console.error("STRUCTURED_ERROR:", JSON.stringify(structuredLog));
  }
}

/**
 * Log warnings with consistent format
 */
export function logWarning(context: string, message: string, data?: any): void {
  if (!shouldLog(LogLevel.WARN)) return;

  const logEntry = createLogEntry(LogLevel.WARN, context, message, data);
  console.warn(logEntry);
}

/**
 * Log debug information (can be easily disabled in production)
 */
export function logDebug(context: string, message: string, data?: any): void {
  if (!shouldLog(LogLevel.DEBUG) || !logConfig.enableDebug) return;

  const logEntry = createLogEntry(LogLevel.DEBUG, context, message, data);
  console.log(logEntry);
}

/**
 * Log info messages with conditional output
 */
export function logInfo(context: string, message: string, data?: any): void {
  if (!shouldLog(LogLevel.INFO)) return;

  const logEntry = createLogEntry(LogLevel.INFO, context, message, data);
  console.log(logEntry);
}

/**
 * Safe async operation wrapper with standardized error handling
 * Eliminates repetitive try-catch blocks with identical error handling
 */
export async function safeAsync<T>(
  operation: () => Promise<T>,
  context: string,
  errorMessage: string = 'Operation failed'
): Promise<T | null> {
  try {
    return await operation();
  } catch (error) {
    logError(context, error as Error, { operation: errorMessage });
    return null;
  }
}

/**
 * Safe async operation wrapper that throws on error but logs consistently
 */
export async function safeAsyncThrow<T>(
  operation: () => Promise<T>,
  context: string,
  errorMessage: string = 'Operation failed'
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    logError(context, error as Error, { operation: errorMessage });
    throw error;
  }
}

/**
 * Performance logging wrapper
 */
export async function logPerformance<T>(
  operation: () => Promise<T>,
  context: string,
  operationName: string
): Promise<T> {
  const startTime = performance.now();

  try {
    const result = await operation();
    const duration = performance.now() - startTime;

    logGameEvent('performance', {
      operation: operationName,
      duration: Math.round(duration),
      success: true
    }, context);

    return result;
  } catch (error) {
    const duration = performance.now() - startTime;

    logGameEvent('performance', {
      operation: operationName,
      duration: Math.round(duration),
      success: false,
      error: (error as Error).message
    }, context);

    throw error;
  }
}

/**
 * Common logging contexts for consistency
 */
export const LogContext = {
  GAME: 'GAME',
  AUTH: 'AUTH',
  API: 'API',
  STATS: 'STATS',
  SESSION: 'SESSION',
  STATIC: 'STATIC',
  WEBAUTHN: 'WEBAUTHN',
  DATABASE: 'DATABASE',
  PERFORMANCE: 'PERFORMANCE',
  SERVER: 'SERVER',
  CLIENT: 'CLIENT'
} as const;

/**
 * Simple logging functions for easy migration from console.log
 * These provide conditional logging with consistent formatting
 */
export const Logger = {
  /**
   * Replace console.log statements with conditional logging
   */
  log(context: string, message: string, ...args: any[]): void {
    if (!shouldLog(LogLevel.INFO)) return;
    if (args.length > 0) {
      logInfo(context, message, args);
    } else {
      logInfo(context, message);
    }
  },

  /**
   * Replace console.error statements with conditional logging
   */
  error(context: string, message: string, error?: Error | any): void {
    if (!shouldLog(LogLevel.ERROR)) return;
    if (error instanceof Error) {
      logError(context, error);
    } else {
      console.error(createLogEntry(LogLevel.ERROR, context, message, error));
    }
  },

  /**
   * Replace console.warn statements with conditional logging
   */
  warn(context: string, message: string, data?: any): void {
    logWarning(context, message, data);
  },

  /**
   * Debug logging that's disabled in production by default
   */
  debug(context: string, message: string, data?: any): void {
    logDebug(context, message, data);
  }
};

/**
 * Generate client-side logging code that respects environment settings
 */
export function generateClientLogger(): string {
  const clientConfig = {
    enableLogs: logConfig.enableClientLogs,
    logLevel: logConfig.level,
    isProduction
  };

  return `
// Client-side conditional logging
window.gameLogger = (function() {
  const config = ${JSON.stringify(clientConfig)};
  const logLevels = { DEBUG: 0, INFO: 1, WARN: 2, ERROR: 3 };
  const minLevel = logLevels[config.logLevel] || 1;
  
  function shouldLog(level) {
    return config.enableLogs && logLevels[level] >= minLevel;
  }
  
  return {
    log: function(context, message, data) {
      if (!shouldLog('INFO')) return;
      console.log('[' + context + '] ' + message, data || '');
    },
    error: function(context, message, data) {
      if (!shouldLog('ERROR')) return;
      console.error('[' + context + '] ' + message, data || '');
    },
    warn: function(context, message, data) {
      if (!shouldLog('WARN')) return;
      console.warn('[' + context + '] ' + message, data || '');
    },
    debug: function(context, message, data) {
      if (!shouldLog('DEBUG')) return;
      console.log('[DEBUG][' + context + '] ' + message, data || '');
    }
  };
})();
`;
}

/**
 * Result type integration for consistent error handling with logging
 */
export function logResult<T, E extends Error>(
  result: { ok: boolean; value?: T; error?: E },
  context: string,
  operation: string
): typeof result {
  if (!result.ok && result.error) {
    logError(context, result.error, { operation });
  } else {
    logDebug(context, `${operation} completed successfully`);
  }
  return result;
}

/**
 * Higher-order function for logging function execution
 */
export function withLogging<T extends (...args: any[]) => any>(
  fn: T,
  context: string,
  operation: string
): T {
  return ((...args: Parameters<T>) => {
    logDebug(context, `Starting ${operation}`, { args: args.slice(0, 2) }); // Only log first 2 args for brevity
    
    try {
      const result = fn(...args);
      
      if (result instanceof Promise) {
        return result.then(
          (value) => {
            logDebug(context, `${operation} completed successfully`);
            return value;
          },
          (error) => {
            logError(context, error, { operation });
            throw error;
          }
        );
      }
      
      logDebug(context, `${operation} completed successfully`);
      return result;
    } catch (error) {
      logError(context, error as Error, { operation });
      throw error;
    }
  }) as T;
}

/**
 * Create a scoped logger for a specific context
 */
export function createContextLogger(context: string) {
  return {
    log: (message: string, data?: any) => Logger.log(context, message, data),
    error: (message: string, error?: Error | any) => Logger.error(context, message, error),
    warn: (message: string, data?: any) => Logger.warn(context, message, data),
    debug: (message: string, data?: any) => Logger.debug(context, message, data),
    
    // Specialized methods for this context
    withLogging: <T extends (...args: any[]) => any>(fn: T, operation: string) => 
      withLogging(fn, context, operation),
    
    logResult: <T, E extends Error>(result: { ok: boolean; value?: T; error?: E }, operation: string) =>
      logResult(result, context, operation)
  };
}

/**
 * Game-specific logging helpers
 */
export const GameLogger = {
  /**
   * Log game creation
   */
  gameCreated(gameId: string, username: string, difficulty: string, category: string): void {
    logGameEvent('game_created', { gameId, username, difficulty, category });
  },

  /**
   * Log game completion
   */
  gameCompleted(gameId: string, username: string, result: 'won' | 'lost' | 'timeout', duration: number): void {
    logGameEvent('game_completed', { gameId, username, result, duration });
  },

  /**
   * Log guess made
   */
  guessMade(gameId: string, username: string, letter: string, correct: boolean): void {
    logGameEvent('guess_made', { gameId, username, letter, correct });
  },

  /**
   * Log hint used
   */
  hintUsed(gameId: string, username: string): void {
    logGameEvent('hint_used', { gameId, username });
  },

  /**
   * Log daily limit reached
   */
  dailyLimitReached(username: string, gamesPlayed: number): void {
    logGameEvent('daily_limit_reached', { username, gamesPlayed });
  }
} as const;