/**
 * Unified response factory to consolidate all HTTP response creation patterns
 * Provides a consistent, type-safe API for creating responses across the application
 */

import { CONTENT_TYPE } from "../core/constants.ts";
import { CookieCreators, BatchCookies } from "./cookies.ts";
import { createErrorResponse as createError, <PERSON>rro<PERSON> } from "./errors.ts";
import type { ResponseFormat } from "./errors.ts";

/**
 * HTMX-specific headers for enhanced functionality
 */
export const HTMX_HEADERS = {
  REQUEST: "HX-Request",
  TARGET: "HX-Target", 
  TRIGGER: "HX-Trigger",
  LOCATION: "HX-Location",
  REDIRECT: "HX-Redirect",
  REFRESH: "HX-Refresh",
  PUSH_URL: "HX-Push-Url",
  REPLACE_URL: "HX-Replace-Url"
} as const;

/**
 * Common response options interface
 */
export interface ResponseOptions {
  readonly status?: number;
  readonly headers?: Record<string, string>;
  readonly cookies?: string[];
}

/**
 * HTMX-specific response options
 */
export interface HtmxResponseOptions extends ResponseOptions {
  readonly trigger?: string | Record<string, unknown>;
  readonly target?: string;
  readonly location?: string;
  readonly redirect?: string;
  readonly refresh?: boolean;
  readonly pushUrl?: string;
  readonly replaceUrl?: string;
  readonly sessionId?: string;
}

/**
 * File response options for static content
 */
export interface FileResponseOptions extends ResponseOptions {
  readonly cacheControl?: string;
  readonly lastModified?: Date;
  readonly etag?: string;
}

/**
 * API response data structure
 */
export interface ApiResponseData<T = unknown> {
  readonly success: boolean;
  readonly data?: T;
  readonly error?: string;
  readonly message?: string;
  readonly meta?: Record<string, unknown>;
}

/**
 * Utility functions for response creation
 */
const createHeaders = (
  baseHeaders: Record<string, string>,
  options?: ResponseOptions
): Headers => {
  const allHeaders = {
    ...baseHeaders,
    ...options?.headers
  };

  // Add cookies if provided
  if (options?.cookies && options.cookies.length > 0) {
    allHeaders["Set-Cookie"] = BatchCookies.combine(...options.cookies);
  }

  return new Headers(allHeaders);
};

const processHtmxOptions = (options: HtmxResponseOptions = {}): Record<string, string> => {
  const htmxHeaders: Record<string, string> = {};

  if (options.trigger) {
    htmxHeaders[HTMX_HEADERS.TRIGGER] = typeof options.trigger === "string" 
      ? options.trigger 
      : JSON.stringify(options.trigger);
  }
  
  if (options.target) htmxHeaders[HTMX_HEADERS.TARGET] = options.target;
  if (options.location) htmxHeaders[HTMX_HEADERS.LOCATION] = options.location;
  if (options.redirect) htmxHeaders[HTMX_HEADERS.REDIRECT] = options.redirect;
  if (options.refresh) htmxHeaders[HTMX_HEADERS.REFRESH] = "true";
  if (options.pushUrl) htmxHeaders[HTMX_HEADERS.PUSH_URL] = options.pushUrl;
  if (options.replaceUrl) htmxHeaders[HTMX_HEADERS.REPLACE_URL] = options.replaceUrl;

  return htmxHeaders;
};

/**
 * Unified response factory with comprehensive methods
 */
export const ResponseFactory = {
  /**
   * Create HTML responses
   */
  html: {
    /**
     * Basic HTML response
     */
    create: (content: string, options?: ResponseOptions): Response => {
      const headers = createHeaders({ "Content-Type": CONTENT_TYPE.HTML }, options);
      return new Response(content, { 
        status: options?.status ?? 200, 
        headers 
      });
    },

    /**
     * HTML response with session cookie
     */
    withSession: (content: string, sessionId: string, options?: ResponseOptions): Response => {
      const sessionCookie = CookieCreators.gameSession(sessionId);
      const cookies = options?.cookies ? [sessionCookie, ...options.cookies] : [sessionCookie];
      
      return ResponseFactory.html.create(content, {
        ...options,
        cookies
      });
    },

    /**
     * HTMX-aware HTML response
     */
    htmx: (content: string, options?: HtmxResponseOptions): Response => {
      const htmxHeaders = processHtmxOptions(options);
      const baseHeaders = { "Content-Type": CONTENT_TYPE.HTML, ...htmxHeaders };
      
      // Add session cookie if provided
      const cookies = options?.cookies ? [...options.cookies] : [];
      if (options?.sessionId) {
        cookies.push(CookieCreators.gameSession(options.sessionId));
      }

      const headers = createHeaders(baseHeaders, { ...options, cookies });
      return new Response(content, { 
        status: options?.status ?? 200, 
        headers 
      });
    }
  },

  /**
   * Create JSON responses
   */
  json: {
    /**
     * Basic JSON response
     */
    create: <T>(data: T, options?: ResponseOptions): Response => {
      const headers = createHeaders({ "Content-Type": CONTENT_TYPE.JSON }, options);
      return new Response(JSON.stringify(data), { 
        status: options?.status ?? 200, 
        headers 
      });
    },

    /**
     * Structured API response
     */
    api: <T>(
      success: boolean, 
      payload?: { data?: T; error?: string; message?: string; meta?: Record<string, unknown> },
      options?: ResponseOptions
    ): Response => {
      const responseData: ApiResponseData<T> = {
        success,
        ...payload
      };

      return ResponseFactory.json.create(responseData, options);
    },

    /**
     * Success API response
     */
    success: <T>(data?: T, message?: string, options?: ResponseOptions): Response => {
      return ResponseFactory.json.api(true, { data, message }, options);
    },

    /**
     * Error API response
     */
    error: (error: string, message?: string, options?: ResponseOptions): Response => {
      return ResponseFactory.json.api(false, { error, message }, {
        ...options,
        status: options?.status ?? 400
      });
    }
  },

  /**
   * Create redirect responses
   */
  redirect: {
    /**
     * Standard HTTP redirect
     */
    http: (url: string, status: number = 302, options?: ResponseOptions): Response => {
      const headers = createHeaders({ "Location": url }, options);
      return new Response(null, { status, headers });
    },

    /**
     * HTMX redirect (client-side)
     */
    htmx: (url: string, options?: ResponseOptions): Response => {
      const headers = createHeaders({ [HTMX_HEADERS.REDIRECT]: url }, options);
      return new Response("", { 
        status: options?.status ?? 200, 
        headers 
      });
    }
  },

  /**
   * Create static file responses
   */
  static: {
    /**
     * Static file response with content type detection
     */
    file: (content: string | Uint8Array, filePath: string, options?: FileResponseOptions): Response => {
      let contentType: string = CONTENT_TYPE.PLAIN_TEXT;
      
      // Content type detection
      if (filePath.endsWith('.css')) {
        contentType = CONTENT_TYPE.CSS;
      } else if (filePath.endsWith('.js')) {
        contentType = CONTENT_TYPE.JAVASCRIPT;
      } else if (filePath.endsWith('.html')) {
        contentType = CONTENT_TYPE.HTML;
      } else if (filePath.endsWith('.json')) {
        contentType = CONTENT_TYPE.JSON;
      } else if (filePath.endsWith('.png')) {
        contentType = "image/png";
      } else if (filePath.endsWith('.jpg') || filePath.endsWith('.jpeg')) {
        contentType = "image/jpeg";
      } else if (filePath.endsWith('.svg')) {
        contentType = "image/svg+xml";
      }

      const baseHeaders: Record<string, string> = { "Content-Type": contentType };
      
      // Add cache headers if provided
      if (options?.cacheControl) {
        baseHeaders["Cache-Control"] = options.cacheControl;
      }
      if (options?.lastModified) {
        baseHeaders["Last-Modified"] = options.lastModified.toUTCString();
      }
      if (options?.etag) {
        baseHeaders["ETag"] = options.etag;
      }

      const headers = createHeaders(baseHeaders, options);
      return new Response(content, { 
        status: options?.status ?? 200, 
        headers 
      });
    }
  },

  /**
   * Create error responses using centralized error handling
   */
  error: {
    /**
     * Create error response with automatic type detection
     */
    create: (error: Error | string, format: ResponseFormat = "html", context?: string): Response => {
      const errorObj = typeof error === "string" ? Errors.internal(error) : error;
      return createError(errorObj, { format, context });
    },

    /**
     * Create validation error response
     */
    validation: (message: string, format: ResponseFormat = "html"): Response => {
      return createError(Errors.validation(message), { format });
    },

    /**
     * Create not found error response
     */
    notFound: (resource?: string, format: ResponseFormat = "html"): Response => {
      const message = resource ? `${resource} not found` : "Resource not found";
      return createError(Errors.notFound(message), { format });
    },

    /**
     * Create internal server error response
     */
    internal: (message?: string, format: ResponseFormat = "html"): Response => {
      return createError(Errors.internal(message), { format });
    }
  },

  /**
   * HTMX utilities
   */
  htmx: {
    /**
     * Check if request is from HTMX
     */
    isRequest: (request: Request): boolean => {
      return request.headers.get(HTMX_HEADERS.REQUEST) === "true";
    },

    /**
     * Create HTMX trigger-only response
     */
    trigger: (event: string | Record<string, unknown>, options?: ResponseOptions): Response => {
      const triggerValue = typeof event === "string" ? event : JSON.stringify(event);
      const headers = createHeaders({ [HTMX_HEADERS.TRIGGER]: triggerValue }, options);
      return new Response("", { 
        status: options?.status ?? 200, 
        headers 
      });
    },

    /**
     * Create adaptive response (HTMX-aware)
     */
    adaptive: (
      request: Request,
      content: string,
      fullPageWrapper?: (content: string) => string,
      options?: HtmxResponseOptions
    ): Response => {
      if (ResponseFactory.htmx.isRequest(request)) {
        // HTMX request - return just the content
        return ResponseFactory.html.htmx(content, options);
      } else {
        // Full page request - wrap content if wrapper provided
        const finalContent = fullPageWrapper ? fullPageWrapper(content) : content;
        return ResponseFactory.html.htmx(finalContent, options);
      }
    }
  }
} as const;

/**
 * Convenience exports for backward compatibility
 */
export const createHtmlResponse = ResponseFactory.html.create;
export const createJsonResponse = ResponseFactory.json.create;
export const createHtmlResponseWithSession = ResponseFactory.html.withSession;
export const createStaticResponse = ResponseFactory.static.file;
export const isHtmxRequest = ResponseFactory.htmx.isRequest;

/**
 * Legacy HTMX response object for backward compatibility
 */
export const htmxResponse = {
  html: ResponseFactory.html.htmx,
  redirect: ResponseFactory.redirect.htmx,
  trigger: ResponseFactory.htmx.trigger
};