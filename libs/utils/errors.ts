/**
 * Centralized error handling utilities to standardize error responses
 * and eliminate duplication across auth, game, and API handlers
 */

import { Logger, LogContext } from "./logger.ts";
import { createHtmlResponse, createJsonResponse } from "./http.ts";
import { match } from "./pattern.ts";

/**
 * Standard error types for consistent handling
 */
export type ErrorType = 
  | "ValidationError"
  | "AuthenticationError"
  | "AuthorizationError"
  | "NotFoundError"
  | "RateLimitError"
  | "InternalError"
  | "GameError"
  | "SessionError"
  | "DailyLimitError";

/**
 * Error configuration for different error types
 */
export type ErrorConfig = {
  readonly status: number;
  readonly message: string;
  readonly logLevel: "debug" | "warn" | "error";
  readonly userMessage: string;
};

/**
 * Predefined error configurations
 */
export const ERROR_CONFIGS: Record<ErrorType, ErrorConfig> = {
  ValidationError: {
    status: 400,
    message: "Validation failed",
    logLevel: "warn",
    userMessage: "Please check your input and try again."
  },
  AuthenticationError: {
    status: 401,
    message: "Authentication required",
    logLevel: "warn",
    userMessage: "Please log in to continue."
  },
  AuthorizationError: {
    status: 403,
    message: "Access denied",
    logLevel: "warn",
    userMessage: "You don't have permission to access this resource."
  },
  NotFoundError: {
    status: 404,
    message: "Resource not found",
    logLevel: "warn",
    userMessage: "The requested resource was not found."
  },
  RateLimitError: {
    status: 429,
    message: "Rate limit exceeded",
    logLevel: "warn",
    userMessage: "Too many requests. Please try again later."
  },
  InternalError: {
    status: 500,
    message: "Internal server error",
    logLevel: "error",
    userMessage: "An unexpected error occurred. Please try again."
  },
  GameError: {
    status: 400,
    message: "Game operation failed",
    logLevel: "warn",
    userMessage: "Game operation failed. Please try again."
  },
  SessionError: {
    status: 400,
    message: "Session error",
    logLevel: "warn",
    userMessage: "Session error. Please refresh and try again."
  },
  DailyLimitError: {
    status: 400,
    message: "Daily limit reached",
    logLevel: "warn",
    userMessage: "You've reached your daily game limit."
  }
} as const;

/**
 * Enhanced error class with type information
 */
export class AppError extends Error {
  public readonly type: ErrorType;
  public readonly context?: Record<string, unknown>;

  constructor(
    type: ErrorType,
    message?: string,
    cause?: Error,
    context?: Record<string, unknown>
  ) {
    super(message || ERROR_CONFIGS[type].message);
    this.name = "AppError";
    this.type = type;
    this.context = context;
    // Use the built-in cause property if supported
    if (cause) {
      this.cause = cause;
    }
  }

  get config(): ErrorConfig {
    return ERROR_CONFIGS[this.type];
  }

  get userMessage(): string {
    return this.message === ERROR_CONFIGS[this.type].message 
      ? this.config.userMessage 
      : this.message;
  }

  get status(): number {
    return this.config.status;
  }

  get logLevel(): "debug" | "warn" | "error" {
    return this.config.logLevel;
  }
}

/**
 * Error factory functions for common error types
 */
export const Errors = {
  validation: (message?: string, context?: Record<string, unknown>): AppError =>
    new AppError("ValidationError", message, undefined, context),

  authentication: (message?: string, context?: Record<string, unknown>): AppError =>
    new AppError("AuthenticationError", message, undefined, context),

  authorization: (message?: string, context?: Record<string, unknown>): AppError =>
    new AppError("AuthorizationError", message, undefined, context),

  notFound: (message?: string, context?: Record<string, unknown>): AppError =>
    new AppError("NotFoundError", message, undefined, context),

  rateLimit: (message?: string, context?: Record<string, unknown>): AppError =>
    new AppError("RateLimitError", message, undefined, context),

  internal: (message?: string, cause?: Error, context?: Record<string, unknown>): AppError =>
    new AppError("InternalError", message, cause, context),

  game: (message?: string, context?: Record<string, unknown>): AppError =>
    new AppError("GameError", message, undefined, context),

  session: (message?: string, context?: Record<string, unknown>): AppError =>
    new AppError("SessionError", message, undefined, context),

  dailyLimit: (message?: string, context?: Record<string, unknown>): AppError =>
    new AppError("DailyLimitError", message, undefined, context),

  fromError: (error: Error, type: ErrorType = "InternalError", context?: Record<string, unknown>): AppError => {
    if (error instanceof AppError) {
      return error;
    }
    return new AppError(type, error.message, error, context);
  }
} as const;

/**
 * Response format options
 */
export type ResponseFormat = "json" | "html" | "redirect";

/**
 * Response options for error handling
 */
export type ErrorResponseOptions = {
  readonly format?: ResponseFormat;
  readonly redirectUrl?: string;
  readonly context?: string;
  readonly includeDetails?: boolean;
};

/**
 * Centralized error response creator
 */
export const createErrorResponse = (
  error: Error | AppError | string,
  options: ErrorResponseOptions = {}
): Response => {
  const {
    format = "html",
    redirectUrl,
    context,
    includeDetails = false
  } = options;

  // Convert to AppError for consistent handling
  const appError = typeof error === "string" 
    ? Errors.internal(error)
    : error instanceof AppError 
      ? error 
      : Errors.fromError(error);

  // Log the error with appropriate level
  const logContext = context ? `${context}: ${appError.type}` : appError.type;
  switch (appError.logLevel) {
    case "debug":
      Logger.debug(LogContext.SERVER, logContext, { 
        error: appError.message,
        context: appError.context 
      });
      break;
    case "warn":
      Logger.warn(LogContext.SERVER, logContext, { 
        error: appError.message,
        context: appError.context 
      });
      break;
    case "error":
      Logger.error(LogContext.SERVER, logContext, appError);
      break;
  }

  // Handle redirect format
  if (format === "redirect" && redirectUrl) {
    return new Response(null, {
      status: 302,
      headers: { "Location": redirectUrl }
    });
  }

  // Create error message for user
  const userMessage = includeDetails 
    ? `${appError.userMessage} (${appError.message})`
    : appError.userMessage;

  // Handle JSON format
  if (format === "json") {
    return createJsonResponse({
      success: false,
      error: userMessage,
      type: appError.type,
      ...(includeDetails && { details: appError.message, context: appError.context })
    }, appError.status);
  }

  // Handle HTML format (default)
  return createHtmlResponse(`Error: ${userMessage}`, undefined, appError.status);
};

/**
 * Middleware-style error handler for use in route handlers
 */
export const withErrorHandling = <T extends any[], R>(
  handler: (...args: T) => Promise<R | Response>,
  context?: string
) => {
  return async (...args: T): Promise<R | Response> => {
    try {
      return await handler(...args);
    } catch (error) {
      if (error instanceof Response) {
        return error;
      }
      
      const appError = error instanceof AppError 
        ? error 
        : Errors.fromError(error as Error);

      return createErrorResponse(appError, { 
        context,
        format: "html",
        includeDetails: false
      });
    }
  };
};

/**
 * Parse error from common patterns (like daily limit errors)
 * Uses pattern matching for cleaner and more maintainable error classification
 */
export const parseErrorMessage = (error: Error): {
  type: ErrorType;
  data: Record<string, any>;
} => {
  const message = error.message;
  
  return match(message)
    .when(
      (msg: string) => msg.startsWith('DAILY_LIMIT_REACHED:'),
      (msg: string) => {
        const parts = msg.split(':');
        return {
          type: 'DailyLimitError' as const,
          data: {
            gamesPlayed: parseInt(parts[1]) || 0,
            gamesRemaining: parseInt(parts[2]) || 0
          }
        };
      }
    )
    .when(
      (msg: string) => msg.includes('rate limit'),
      () => ({ type: 'RateLimitError' as const, data: {} })
    )
    .when(
      (msg: string) => msg.includes('not found'),
      () => ({ type: 'NotFoundError' as const, data: {} })
    )
    .when(
      (msg: string) => msg.includes('unauthorized') || msg.includes('authentication'),
      () => ({ type: 'AuthenticationError' as const, data: {} })
    )
    .when(
      (msg: string) => msg.includes('forbidden') || msg.includes('access denied'),
      () => ({ type: 'AuthorizationError' as const, data: {} })
    )
    .otherwise(() => ({ 
      type: 'InternalError' as const, 
      data: { message } 
    }));
};

/**
 * Convert legacy error responses to new format
 */
export const modernizeErrorHandling = {
  /**
   * For auth utilities - replaces createErrorResponse.json
   */
  authJson: (message: string, status: number = 400): Response => {
    const errorType: ErrorType = status === 429 ? "RateLimitError" 
      : status === 404 ? "NotFoundError"
      : status === 401 ? "AuthenticationError"
      : status === 403 ? "AuthorizationError"
      : status >= 500 ? "InternalError"
      : "ValidationError";

    return createErrorResponse(new AppError(errorType, message), { 
      format: "json",
      includeDetails: false 
    });
  },

  /**
   * For general handlers - replaces various createErrorResponse calls
   */
  general: (message: string, status: number = 500): Response => {
    const errorType: ErrorType = status === 404 ? "NotFoundError"
      : status >= 500 ? "InternalError"
      : "GameError";

    return createErrorResponse(new AppError(errorType, message), { 
      format: "html",
      includeDetails: false 
    });
  }
} as const;