/**
 * Centralized cookie utilities to consolidate duplicate cookie handling logic
 * Eliminates duplication across auth and game session management
 */

/**
 * Cookie parsing configuration
 */
export type CookieConfig = {
  readonly name: string;
  readonly pattern: RegExp;
};

/**
 * Predefined cookie configurations for common session types
 */
export const COOKIE_CONFIGS = {
  AUTH_SESSION: {
    name: "session",
    pattern: /(?:^|; )session=([^;]+)/
  },
  GAME_SESSION: {
    name: "hangman_session", 
    pattern: /(?:^|; )hangman_session=([^;]+)/
  },
  ADMIN_SESSION: {
    name: "admin_session",
    pattern: /(?:^|; )admin_session=([^;]+)/
  }
} as const;

/**
 * Generic cookie extraction function that works with any cookie name
 */
export const extractCookie = (request: Request, cookieName: string): string | null => {
  const cookies = request.headers.get("cookie") || "";
  const cookieValue = cookies
    .split(";")
    .map(c => c.trim())
    .find(c => c.startsWith(`${cookieName}=`));

  return cookieValue?.split("=")[1] || null;
};

/**
 * Extract cookie using regex pattern (more efficient for known patterns)
 */
export const extractCookieByPattern = (request: Request, pattern: RegExp): string | null => {
  const cookies = request.headers.get("cookie") || "";
  const match = pattern.exec(cookies);
  return match?.[1] || null;
};

/**
 * Extract cookie using predefined configuration
 */
export const extractCookieByConfig = (request: Request, config: CookieConfig): string | null => {
  return extractCookieByPattern(request, config.pattern);
};

/**
 * Specialized extractors for common session types
 */
export const SessionCookies = {
  /**
   * Extract authentication session ID from request
   */
  extractAuthSession: (request: Request): string | null => 
    extractCookieByConfig(request, COOKIE_CONFIGS.AUTH_SESSION),

  /**
   * Extract game session ID from request  
   */
  extractGameSession: (request: Request): string | null =>
    extractCookieByConfig(request, COOKIE_CONFIGS.GAME_SESSION),

  /**
   * Extract admin session ID from request
   */
  extractAdminSession: (request: Request): string | null =>
    extractCookieByConfig(request, COOKIE_CONFIGS.ADMIN_SESSION),

  /**
   * Extract multiple session types at once
   */
  extractAll: (request: Request) => ({
    auth: SessionCookies.extractAuthSession(request),
    game: SessionCookies.extractGameSession(request),
    admin: SessionCookies.extractAdminSession(request)
  })
} as const;

/**
 * Cookie creation utilities with standardized options
 */
export type CookieOptions = {
  readonly maxAge?: number;
  readonly path?: string;
  readonly secure?: boolean;
  readonly sameSite?: "Strict" | "Lax" | "None";
  readonly httpOnly?: boolean;
};

/**
 * Default cookie options for security
 */
export const DEFAULT_COOKIE_OPTIONS: CookieOptions = {
  maxAge: 86400, // 24 hours
  path: "/",
  secure: false, // Set to true in production with HTTPS
  sameSite: "Strict",
  httpOnly: true
} as const;

/**
 * Create a standardized cookie string
 */
export const createCookieString = (
  name: string,
  value: string,
  options: CookieOptions = {}
): string => {
  const opts = { ...DEFAULT_COOKIE_OPTIONS, ...options };
  
  const attributes = [
    `Path=${opts.path}`,
    opts.httpOnly && "HttpOnly",
    opts.secure && "Secure", 
    `SameSite=${opts.sameSite}`,
    `Max-Age=${opts.maxAge}`
  ].filter(Boolean).join("; ");
  
  return `${name}=${value}; ${attributes}`;
};

/**
 * Create cookie clearing string (expires immediately)
 */
export const createClearCookieString = (name: string, path: string = "/"): string => {
  return `${name}=; Path=${path}; HttpOnly; SameSite=Strict; Max-Age=0`;
};

/**
 * Specialized cookie creators for common session types
 */
export const CookieCreators = {
  /**
   * Create authentication session cookie
   */
  authSession: (sessionId: string, options?: CookieOptions): string =>
    createCookieString(COOKIE_CONFIGS.AUTH_SESSION.name, sessionId, options),

  /**
   * Create game session cookie
   */
  gameSession: (sessionId: string, options?: CookieOptions): string =>
    createCookieString(COOKIE_CONFIGS.GAME_SESSION.name, sessionId, options),

  /**
   * Create admin session cookie
   */
  adminSession: (sessionId: string, options?: CookieOptions): string =>
    createCookieString(COOKIE_CONFIGS.ADMIN_SESSION.name, sessionId, options),

  /**
   * Clear authentication session cookie
   */
  clearAuthSession: (): string =>
    createClearCookieString(COOKIE_CONFIGS.AUTH_SESSION.name),

  /**
   * Clear game session cookie
   */
  clearGameSession: (): string =>
    createClearCookieString(COOKIE_CONFIGS.GAME_SESSION.name),

  /**
   * Clear admin session cookie
   */
  clearAdminSession: (): string =>
    createClearCookieString(COOKIE_CONFIGS.ADMIN_SESSION.name)
} as const;

/**
 * Batch cookie operations for handling multiple cookies at once
 */
export const BatchCookies = {
  /**
   * Combine multiple cookie strings into a single Set-Cookie header value
   */
  combine: (...cookies: string[]): string => {
    return cookies.filter(Boolean).join(", ");
  },

  /**
   * Clear multiple session types at once
   */
  clearAll: (): string => {
    return BatchCookies.combine(
      CookieCreators.clearAuthSession(),
      CookieCreators.clearGameSession(),
      CookieCreators.clearAdminSession()
    );
  }
} as const;