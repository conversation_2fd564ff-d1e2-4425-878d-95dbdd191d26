import { CONTENT_TYPE, SESSION_CONFIG } from "../core/constants.ts";
import { CookieCreators } from "./cookies.ts";

/**
 * HTTP response utilities to eliminate duplication of headers and response creation
 * @deprecated Use the unified ResponseFactory from libs/utils/responses.ts for new code
 */

// Cookie configuration constants
export const COOKIE_CONFIG = {
  MAX_AGE: SESSION_CONFIG.MAX_AGE,
  ATTRIBUTES: "Path=/; HttpOnly; SameSite=Strict"
} as const;

// HTMX headers for better integration
// @deprecated Use HTMX_HEADERS from libs/utils/responses.ts
export const HTMX_HEADERS = {
  REQUEST: "HX-Request",
  TARGET: "HX-Target", 
  TRIGGER: "HX-Trigger",
  LOCATION: "HX-Location",
  REDIRECT: "HX-Redirect",
  REFRESH: "HX-Refresh"
} as const;

/**
 * Create an HTML response with standard headers
 */
export function createHtmlResponse(
  content: string, 
  additionalHeaders?: Record<string, string>,
  status: number = 200
): Response {
  const headers = new Headers({
    "Content-Type": CONTENT_TYPE.HTML,
    ...additionalHeaders
  });
  
  return new Response(content, { status, headers });
}

/**
 * Create a JSON response with standard headers
 */
export function createJsonResponse(
  data: unknown, 
  status: number = 200,
  additionalHeaders?: Record<string, string>
): Response {
  const headers = new Headers({
    "Content-Type": CONTENT_TYPE.JSON,
    ...additionalHeaders
  });
  
  return new Response(JSON.stringify(data), { status, headers });
}

/**
 * Create a consolidated cookie string with customizable name and options
 */
export function createCookie(
  name: string, 
  value: string, 
  options: {
    maxAge?: number;
    path?: string;
    secure?: boolean;
    sameSite?: "Strict" | "Lax" | "None";
    httpOnly?: boolean;
  } = {}
): string {
  const {
    maxAge = COOKIE_CONFIG.MAX_AGE,
    path = "/",
    secure = false,
    sameSite = "Strict",
    httpOnly = true
  } = options;
  
  const attributes = [
    `Path=${path}`,
    httpOnly && "HttpOnly",
    secure && "Secure",
    `SameSite=${sameSite}`,
    `Max-Age=${maxAge}`
  ].filter(Boolean).join("; ");
  
  return `${name}=${value}; ${attributes}`;
}

/**
 * Create a game session cookie string
 * Uses centralized cookie creation for consistency
 */
export function createGameCookie(sessionId: string): string {
  return CookieCreators.gameSession(sessionId);
}

/**
 * Create an authentication cookie string
 * Uses centralized cookie creation for consistency
 */
export function createAuthCookie(sessionId: string): string {
  return CookieCreators.authSession(sessionId);
}

/**
 * Create standard HTML headers with optional additional headers
 */
export function createHtmlHeaders(additionalHeaders?: Record<string, string>): Headers {
  return new Headers({
    "Content-Type": CONTENT_TYPE.HTML,
    ...additionalHeaders
  });
}

/**
 * Create an HTML response with a game session cookie
 */
export function createHtmlResponseWithSession(
  content: string,
  sessionId: string,
  additionalHeaders?: Record<string, string>,
  status: number = 200
): Response {
  const headers = createHtmlHeaders({
    "Set-Cookie": createGameCookie(sessionId),
    ...additionalHeaders
  });
  
  return new Response(content, { status, headers });
}

/**
 * Create an error response with consistent formatting
 * @deprecated Use centralized error handling from libs/utils/errors.ts
 */
export function createErrorResponse(
  message: string, 
  status: number = 500,
  format: 'html' | 'json' = 'html'
): Response {
  if (format === 'json') {
    return createJsonResponse({ error: message }, status);
  }
  
  return createHtmlResponse(`Error: ${message}`, undefined, status);
}

/**
 * Create a static file response with appropriate content type
 */
export function createStaticResponse(
  content: string | Uint8Array,
  filePath: string
): Response {
  let contentType: string = CONTENT_TYPE.PLAIN_TEXT;
  
  if (filePath.endsWith('.css')) {
    contentType = CONTENT_TYPE.CSS;
  } else if (filePath.endsWith('.js')) {
    contentType = CONTENT_TYPE.JAVASCRIPT;
  } else if (filePath.endsWith('.html')) {
    contentType = CONTENT_TYPE.HTML;
  }
  
  return new Response(content, {
    headers: { "Content-Type": contentType }
  });
}

/**
 * Check if request is from HTMX
 */
export function isHtmxRequest(request: Request): boolean {
  return request.headers.get(HTMX_HEADERS.REQUEST) === "true";
}

/**
 * Create HTMX response utilities
 */
export const htmxResponse = {
  /**
   * Create an HTMX-aware HTML response
   */
  html: (content: string, options: {
    trigger?: string;
    target?: string;
    location?: string;
    redirect?: string;
    refresh?: boolean;
    status?: number;
    sessionId?: string;
  } = {}): Response => {
    const {
      trigger,
      target,
      location,
      redirect,
      refresh,
      status = 200,
      sessionId
    } = options;
    
    const headers = new Headers({
      "Content-Type": CONTENT_TYPE.HTML,
      ...(trigger && { [HTMX_HEADERS.TRIGGER]: trigger }),
      ...(target && { [HTMX_HEADERS.TARGET]: target }),
      ...(location && { [HTMX_HEADERS.LOCATION]: location }),
      ...(redirect && { [HTMX_HEADERS.REDIRECT]: redirect }),
      ...(refresh && { [HTMX_HEADERS.REFRESH]: "true" }),
      ...(sessionId && { "Set-Cookie": createGameCookie(sessionId) })
    });
    
    return new Response(content, { status, headers });
  },
  
  /**
   * Create an HTMX redirect response
   */
  redirect: (url: string): Response => {
    return new Response("", {
      status: 200,
      headers: { [HTMX_HEADERS.REDIRECT]: url }
    });
  },
  
  /**
   * Create an HTMX trigger response
   */
  trigger: (event: string, data?: Record<string, unknown>): Response => {
    const triggerValue = data ? JSON.stringify({ [event]: data }) : event;
    return new Response("", {
      status: 200,
      headers: { [HTMX_HEADERS.TRIGGER]: triggerValue }
    });
  }
};