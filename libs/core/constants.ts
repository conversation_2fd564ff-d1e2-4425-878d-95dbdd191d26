/**
 * Shared infrastructure constants
 * These constants are used across multiple applications
 */

// Session Configuration (shared across all apps)
export const SESSION_CONFIG = {
  // Cookie settings
  MAX_AGE: 3600, // 1 hour in seconds
  AUTH_COOKIE_NAME: 'auth_session',
  
  // Session validation
  SESSION_TIMEOUT: 3600000, // 1 hour in milliseconds
  CLEANUP_INTERVAL: 300000   // 5 minutes in milliseconds
} as const;

// Authentication Constants (shared across all apps)
export const AUTH_CONFIG = {
  // Email validation - supports multiple domains
  DISPLAY_EMAIL_DOMAIN: 'dot.com',
  ALLOWED_EMAIL_DOMAINS: ['dot.com', 'deno.dev', 'gmail.com', 'yahoo.com', 'outlook.com', 'hotmail.com'],
  EMAIL_REGEX: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  EMAIL_PATTERN: '[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$',
  
  // WebAuthn settings
  WEBAUTHN_TIMEOUT: 60000, // 1 minute
  CHALLENGE_LENGTH: 32,
  
  // Rate limiting
  MAX_AUTH_ATTEMPTS: 5,
  RATE_LIMIT_WINDOW: 900000 // 15 minutes
} as const;

// HTTP Status Codes (shared across all apps)
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503
} as const;

// Content Types (shared across all apps)
export const CONTENT_TYPE = {
  HTML: 'text/html; charset=utf-8',
  JSON: 'application/json',
  CSS: 'text/css',
  JAVASCRIPT: 'text/javascript',
  PLAIN_TEXT: 'text/plain'
} as const;

// Server Configuration (shared across all apps)
export const SERVER_CONFIG = {
  // Default ports
  DEFAULT_PORT: 8001,
  
  // Timeouts
  REQUEST_TIMEOUT: 30000, // 30 seconds
  
  // Performance
  MAX_REQUEST_SIZE: 10485760, // 10MB
  
  // Environment
  DEVELOPMENT: 'development',
  PRODUCTION: 'production'
} as const;

// Log Levels and Contexts (shared across all apps)
export const LOG_LEVELS = {
  DEBUG: 'DEBUG',
  INFO: 'INFO',
  WARN: 'WARN',
  ERROR: 'ERROR'
} as const;

export const LOG_CONTEXTS = {
  GAME: 'GAME',
  AUTH: 'AUTH',
  API: 'API',
  STATS: 'STATS',
  SESSION: 'SESSION',
  STATIC: 'STATIC',
  WEBAUTHN: 'WEBAUTHN',
  DATABASE: 'DATABASE',
  PERFORMANCE: 'PERFORMANCE'
} as const;

// Regex Patterns (shared patterns)
export const REGEX_PATTERNS = {
  // Letter validation
  LETTER: /^[A-Z]$/,
  LETTERS_ONLY: /^[A-Za-z]+$/,
  
  // Email validation
  EMAIL: AUTH_CONFIG.EMAIL_REGEX,
  
  // File extensions
  CSS_FILE: /\.css$/,
  JS_FILE: /\.js$/,
  HTML_FILE: /\.html$/,
  
  // Path validation
  STATIC_PATH: /^\/static\//,
  API_PATH: /^\/api\//,
  AUTH_PATH: /^\/auth\//
} as const;

// Responsive Breakpoints (shared across apps for consistent UI)
export const BREAKPOINTS = {
  MOBILE: 600, // pixels
  TABLET: 768, // pixels
  DESKTOP: 1024 // pixels
} as const;