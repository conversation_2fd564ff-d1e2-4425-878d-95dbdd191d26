/**
 * Shared types used across multiple applications
 */

// HTTP Route Handler Types
export type RouteHandler = (
  request: Request, 
  params: Record<string, string>
) => Promise<Response>;

export type AuthenticatedRouteHandler<T = any> = (
  request: Request, 
  params: Record<string, string>, 
  authState: T
) => Promise<Response>;

// Router Types
export interface Route {
  readonly path: string;
  readonly handler: RouteHandler;
}

export type Router = (request: Request, pathname: string) => Promise<Response>;

// HTTP Response Types
export interface ApiResponse<T = any> {
  readonly success: boolean;
  readonly data?: T;
  readonly error?: string;
  readonly message?: string;
}

// Session Types (generic)
export interface BaseSession {
  readonly created: number;
}

// Logging Types
export type LogLevel = 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';
export type LogContext = 'GAME' | 'AUTH' | 'API' | 'STATS' | 'SESSION' | 'STATIC' | 'WEBAUTHN' | 'DATABASE' | 'PERFORMANCE';

export interface LogEntry {
  readonly level: LogLevel;
  readonly context: LogContext;
  readonly message: string;
  readonly timestamp: number;
  readonly metadata?: Record<string, any>;
}

// Rate Limiting Types
export interface RateLimitResult {
  readonly allowed: boolean;
  readonly resetTime: number;
  readonly remaining: number;
  readonly response?: Response;
  readonly rateLimitHeaders?: Record<string, string>;
}

// Environment Types
export type Environment = 'development' | 'production' | 'test';

// Email Types (shared across apps that use email)
export interface EmailTemplate {
  readonly subject: string;
  readonly html: string;
  readonly text: string;
}

export interface EmailConfig {
  readonly apiKey: string;
  readonly fromEmail: string;
  readonly fromName: string;
  readonly baseUrl: string;
}