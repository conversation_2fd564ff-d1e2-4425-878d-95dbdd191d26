// Simplified Auth Types - Only what's actually used

// Basic Result type for error handling
export type Result<T, E> = 
  | { readonly tag: "ok"; readonly value: T }
  | { readonly tag: "error"; readonly error: E };

export const ok = <T, E>(value: T): Result<T, E> => ({ tag: "ok", value });
export const error = <T, E>(error: E): Result<T, E> => ({ tag: "error", error });

// Validation error type  
export type ValidationError = {
  readonly field: string;
  readonly message: string;
  readonly code: string;
};

// Auth types used by kv.ts
export type User = {
  readonly id: Uint8Array;
  readonly username: string;
  readonly displayName: string;
  readonly createdAt: number;
};

export type Session = {
  readonly id: string;
  readonly username: string;
  readonly created: number;
  readonly lastAccessed: number;
  readonly metadata?: Record<string, unknown>;
};

// Basic auth state for middleware
export type AuthState = 
  | { readonly tag: "unauthenticated" }
  | { readonly tag: "authenticated"; readonly user: User; readonly session: Session };