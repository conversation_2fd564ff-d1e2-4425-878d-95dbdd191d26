import { getSession, getUser } from "./kv.ts";
import { AuthState } from "./types.ts";
import { SessionCookies } from "../utils/cookies.ts";

// Simplified auth state for basic authentication
export type SimpleAuthState = {
  isAuthenticated: boolean;
  username?: string;
};

/**
 * Basic authentication middleware that checks auth sessions
 * Returns simplified auth state for easy consumption
 */
export async function requireAuth(req: Request): Promise<SimpleAuthState | Response> {
  // Extract auth session using centralized cookie utilities
  const authSessionId = SessionCookies.extractAuthSession(req);

  if (authSessionId) {
    const authSession = await getSession(authSessionId);
    
    if (authSession) {
      return {
        isAuthenticated: true,
        username: authSession.username,
      };
    }
  }

  return new Response(null, {
    status: 302,
    headers: { "Location": "/login" },
  });
}

/**
 * Extended authentication that includes full auth state
 * Returns the complete AuthState with user and session details
 */
export async function getAuthState(req: Request): Promise<AuthState> {
  // Extract auth session using centralized cookie utilities
  const authSessionId = SessionCookies.extractAuthSession(req);

  if (authSessionId) {
    const authSession = await getSession(authSessionId);
    if (authSession) {
      const user = await getUser(authSession.username);
      if (user) {
        return {
          tag: "authenticated",
          user,
          session: authSession,
        };
      }
    }
  }

  return { tag: "unauthenticated" };
}

/**
 * Extract username from request (useful for apps that just need the username)
 */
export async function getUsername(req: Request): Promise<string | null> {
  const authResult = await requireAuth(req);
  
  if (authResult instanceof Response) {
    return null;
  }
  
  return authResult.isAuthenticated ? authResult.username || null : null;
}