// Types
export type { 
  User, 
  Session, 
  AuthState, 
  Result, 
  ValidationError 
} from "./types.ts";

// Middleware
export { 
  requireAuth, 
  getAuthState, 
  getUsername,
  type SimpleAuthState 
} from "./middleware.ts";

// Protected routes
export { 
  withAuth, 
  createProtectedHand<PERSON>, 
  isAuthenticated<PERSON><PERSON><PERSON>,
  type AuthenticatedH<PERSON>ler,
  type RouteHandler 
} from "./protectedRoute.ts";

// Database operations
export { 
  getKv, 
  closeKv, 
  getUser, 
  createUser, 
  updateUser, 
  createSession, 
  getSession, 
  deleteSession, 
  cleanupExpiredSessions,
  // Statistics and game functions
  getNextWinSequence,
  recordWin,
  getTotalWins,
  getRecentWins,
  getUserStatistics,
  updateUserStatistics,
  updatePlayerStanding,
  getPlayerStandings,
  getUserRank,
  getDailyGameCount,
  incrementDailyGameCount,
  checkDailyLimit,
  cleanupOldDailyRecords,
  DAILY_GAME_LIMIT,
  // Types for statistics
  type WinRecord,
  type UserStatistics,
  type PlayerStanding,
  type UserDailyGames
} from "./kv.ts";

// Auth routes
export { handleAuth } from "./routes.ts";

// Configuration
export { AUTH_CONFIG, getRequestAuthConfig } from "./config.ts";

// Email utilities
export { 
  generateVerificationToken, 
  sendVerificationEmail, 
  isValidEmailFormat, 
  getEmailDomain 
} from "./email.ts";

// Consolidated auth utilities
export {
  createAuthResponse,
  createErrorResponse,
  handleAuthError,
  type AuthResponse
} from "./authUtils.ts";

// General utilities
export { 
  toB64, 
  fromB64, 
  fromBase64Url, 
  generateSecureId, 
  generateUserId, 
  ensureUserIdUint8Array, 
  generateSessionId, 
  now 
} from "./utils.ts";