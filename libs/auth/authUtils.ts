import { Logger, LogContext } from "../utils/logger.ts";
import { AUTH_CONFIG } from "../core/constants.ts";
import { modernizeErrorHandling } from "../utils/errors.ts";
import { match } from "../utils/pattern.ts";

// Types for consolidated auth responses
export type AuthResponse = {
  success: boolean;
  redirect?: string;
  error?: string;
  [key: string]: any;
};


// HTML template utilities for auth responses
const createAuthHtml = (title: string, content: string, isSuccess = false): string => {
  const statusClass = isSuccess ? "success" : "error";
  const bgColor = isSuccess ? "#d4edda" : "#f8d7da";
  const borderColor = isSuccess ? "#c3e6cb" : "#f5c6cb";
  const textColor = isSuccess ? "#155724" : "#721c24";
  
  return `
<!DOCTYPE html>
<html>
  <head>
    <title>${title}</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
      body { 
        font-family: Arial, sans-serif; 
        max-width: 600px; 
        margin: 50px auto; 
        padding: 20px; 
        text-align: center; 
      }
      .${statusClass} { 
        color: ${textColor}; 
        background: ${bgColor}; 
        border: 1px solid ${borderColor}; 
        padding: 15px; 
        border-radius: 4px; 
        margin: 20px 0; 
      }
      .button { 
        display: inline-block; 
        background: ${isSuccess ? "#28a745" : "#007bff"}; 
        color: white; 
        padding: 10px 20px; 
        text-decoration: none; 
        border-radius: 4px; 
        margin: 10px; 
      }
      .game-icon { 
        font-size: 48px; 
        margin-bottom: 20px; 
      }
    </style>
  </head>
  <body>
    ${content}
    <a href="/" class="button">Go to Homepage</a>
  </body>
</html>
  `.trim();
};

// Create standardized auth responses
export const createAuthResponse = {
  success: (title: string, message: string, icon = "🎮"): Response => {
    const content = `
      <div class="game-icon">${icon}</div>
      <h1>✅ ${title}</h1>
      <div class="success">
        <strong>${message}</strong>
      </div>
    `;
    return new Response(createAuthHtml(title, content, true), {
      status: 200,
      headers: { "Content-Type": "text/html" }
    });
  },
  
  error: (title: string, message: string, status = 400): Response => {
    const content = `
      <h1>🚫 ${title}</h1>
      <div class="error">${message}</div>
    `;
    return new Response(createAuthHtml(title, content), {
      status,
      headers: { "Content-Type": "text/html" }
    });
  },
  
};

// Maintain backward compatibility while using centralized error handling
export const createErrorResponse = {
  json: modernizeErrorHandling.authJson
};


// Standardized error handling for auth operations using pattern matching
export const handleAuthError = (error: Error, context: string): Response => {
  Logger.error(LogContext.AUTH, `${context} error:`, error);
  
  return match(error.message)
    .when(
      (msg: string) => msg.includes('rate limit'),
      () => modernizeErrorHandling.authJson("Rate limit exceeded. Please try again later.", 429)
    )
    .when(
      (msg: string) => msg.includes('invalid email'),
      () => modernizeErrorHandling.authJson("Invalid email address format.", 400)
    )
    .when(
      (msg: string) => msg.includes('user not found'),
      () => modernizeErrorHandling.authJson("User not found. Please register first.", 404)
    )
    .otherwise(() => modernizeErrorHandling.authJson("An unexpected error occurred. Please try again.", 500));
};

// Validate email domain using shared constants
export const validateEmailDomain = (email: string): boolean => {
  return AUTH_CONFIG.EMAIL_REGEX.test(email);
};

// Create session cookie with consistent settings
export const createSessionCookie = (sessionId: string): string => {
  return [
    `session=${sessionId}`,
    `Path=/`,
    `HttpOnly`,
    `SameSite=Strict`,
    `Max-Age=86400`, // 24 hours
  ].join("; ");
};

// Clear session cookie
export const clearSessionCookie = (): string => {
  return [
    `session=`,
    `Path=/`,
    `HttpOnly`,
    `Secure`,
    `SameSite=Strict`,
    `Max-Age=0`,
  ].join("; ");
};