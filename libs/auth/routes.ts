import { 
  getUser, 
  createUser, 
  createSession, 
  deleteSession
} from "./kv.ts";
import { generateSessionId, generateUserId } from "./utils.ts";
import { match } from "../utils/pattern.ts";
import { Logger, LogContext } from "../utils/logger.ts";
import { 
  createErrorResponse, 
  validateEmailDomain, 
  handleAuthError 
} from "./authUtils.ts";
import { CookieCreators, SessionCookies } from "../utils/cookies.ts";

// Helper function to handle user login with auto-registration
async function handleUserLogin(req: Request): Promise<Response> {
  Logger.log(LogContext.AUTH, "=== User Login ===");
  
  try {
    const { username } = await req.json();
    
    if (!username) {
      return createErrorResponse.json("Email address is required", 400);
    }
    
    Logger.log(LogContext.AUTH, "Login attempt", { username });
    
    // Validate email domain
    if (!validateEmailDomain(username)) {
      return createErrorResponse.json("Email must be from domain", 400);
    }
    
    // Check if user exists
    let user = await getUser(username);
    let isNewUser = false;
    
    if (!user) {
      // Auto-register new user
      Logger.log(LogContext.AUTH, "Auto-registering new user", { username });
      user = await createUser(username, username.split('@')[0], generateUserId());
      isNewUser = true;
    }
    
    // Create session for user
    const sessionId = generateSessionId();
    await createSession(sessionId, username);
    const cookie = CookieCreators.authSession(sessionId);
    
    Logger.log(LogContext.AUTH, "Login successful", { username, sessionId, isNewUser });
    
    return new Response(JSON.stringify({
      success: true,
      message: isNewUser ? "Welcome! Your account has been created." : "Login successful!",
      isNewUser
    }), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        "Set-Cookie": cookie
      }
    });
  } catch (error) {
    Logger.error(LogContext.AUTH, "Error during login", error as Error);
    return createErrorResponse.json("Login failed. Please try again.", 500);
  }
}

// Main auth router function
export async function handleAuth(req: Request): Promise<Response> {
  const url = new URL(req.url);
  const { pathname } = url;
  const method = req.method;

  Logger.debug(LogContext.AUTH, "Auth request", { pathname, method });

  try {
    return await match({ pathname, method })
      .with({ pathname: "/auth/login", method: "POST" }, () => 
        handleUserLogin(req))
      .with({ pathname: "/auth/logout", method: "POST" }, async () => {
        // Handle logout immediately using centralized cookie extraction
        const sessionId = SessionCookies.extractAuthSession(req);
        
        if (sessionId) {
          try {
            await deleteSession(sessionId);
          } catch (error) {
            Logger.debug(LogContext.AUTH, "Error during logout cleanup:", error);
          }
        }

        const cookie = CookieCreators.clearAuthSession();
        return new Response("", {
          status: 200,
          headers: {
            "Set-Cookie": cookie,
            "HX-Location": "/login"
          },
        });
      })
      .otherwise(() => {
        Logger.warn(LogContext.AUTH, "Route not found", { pathname, method });
        return new Response("Not found", { status: 404 });
      });
  } catch (error) {
    Logger.error(LogContext.AUTH, "Auth handler error", error as Error);
    return handleAuthError(error as Error, "Auth handler");
  }
}