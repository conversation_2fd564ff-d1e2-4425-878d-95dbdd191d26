// Email verification service for authentication
import { Logger, LogContext } from "../utils/logger.ts";

// Load environment variables from .env file if it exists (development only)
// Try multiple possible locations for .env file
const isProduction = !!Deno.env.get("DENO_DEPLOYMENT_ID") || Deno.env.get("DENO_ENV") === "production";
const envPaths = [".env", "./apps/hangman/.env", "../../.env"];
let envLoaded = false;

// Only load .env files in development to prevent overriding production environment variables
if (!isProduction) {
  for (const envPath of envPaths) {
    try {
      const env = await Deno.readTextFile(envPath);
      env.split("\n").forEach(line => {
        const trimmedLine = line.trim();
        if (trimmedLine && !trimmedLine.startsWith("#")) {
          const firstEquals = trimmedLine.indexOf("=");
          if (firstEquals > 0) {
            const key = trimmedLine.substring(0, firstEquals).trim();
            const value = trimmedLine.substring(firstEquals + 1).trim();
            // Only set if not already set (don't override existing env vars)
            if (key && value && !Deno.env.get(key)) {
              Deno.env.set(key, value);
            }
          }
        }
      });
      Logger.log(LogContext.AUTH, `📄 Loaded .env file from: ${envPath}`);
      envLoaded = true;
      break;
    } catch {
      // Try next path
    }
  }
}

if (!envLoaded && !isProduction) {
  Logger.debug(LogContext.AUTH, "No .env file found, using system environment variables");
}

// Email configuration with debugging
const rawApiKey = Deno.env.get("RESEND_API_KEY");

const EMAIL_CONFIG = {
  API_KEY: (rawApiKey || "demo-key").trim(), // Trim whitespace
  FROM_EMAIL: "<EMAIL>", // Use your verified domain
  FROM_NAME: "Hangman Game",
  BASE_URL: Deno.env.get("BASE_URL") || "http://localhost:8001",
  // Always send real emails when API key is provided (both dev and prod)
  FORCE_REAL_EMAIL: true,
} as const;

// DEBUG: Log EMAIL_CONFIG initialization
Logger.log(LogContext.AUTH, "EMAIL CONFIG INITIALIZED", {
  BASE_URL: EMAIL_CONFIG.BASE_URL,
  isProduction,
  deploymentId: Deno.env.get("DENO_DEPLOYMENT_ID"),
  denoEnv: Deno.env.get("DENO_ENV"),
  envLoaded
});

// Generate a secure verification token
export function generateVerificationToken(): string {
  return crypto.randomUUID();
}

// Create verification email HTML template
function createVerificationEmailHtml(email: string, verificationUrl: string): string {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Verify your email - Hangman Game</title>
  <style>
    body { font-family: 'Inter', Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f5f5f5; }
    .container { max-width: 600px; margin: 20px auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
    .content { padding: 30px; }
    .button { display: inline-block; background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: 600; margin: 20px 0; }
    .footer { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; font-size: 14px; color: #666; }
    .game-icon { font-size: 48px; margin-bottom: 10px; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="game-icon">🎮</div>
      <h1>Welcome to Hangman!</h1>
      <p>Please verify your email address to start playing</p>
    </div>
    
    <div class="content">
      <h2>Hi there! 👋</h2>
      <p>Thanks for signing up for the Hangman Game! To complete your registration and start playing, please verify your email address by clicking the button below:</p>
      
      <p style="text-align: center;">
        <a href="${verificationUrl}" class="button">Verify Email Address</a>
      </p>
      
      <p>Or copy and paste this link into your browser:</p>
      <p style="word-break: break-all; background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace;">
        ${verificationUrl}
      </p>
      
      <div style="margin-top: 30px; padding: 20px; background: #e8f4fd; border-radius: 6px; border-left: 4px solid #667eea;">
        <h3>🎯 What's waiting for you:</h3>
        <ul style="margin: 10px 0;">
          <li>🎲 Daily word challenges with multiple difficulty levels</li>
          <li>⏰ 60-second time challenges</li>
          <li>💡 Smart hint system to help when you're stuck</li>
          <li>🏆 Compete on the global leaderboard</li>
          <li>📊 Track your progress and win streaks</li>
        </ul>
      </div>
      
      <p><strong>This verification link will expire in 24 hours.</strong></p>
      <p>If you didn't create an account with us, you can safely ignore this email.</p>
    </div>
    
    <div class="footer">
      <p>This email was sent to ${email}</p>
      <p>Hangman Game - Cooked with ❤️ by the development team</p>
    </div>
  </div>
</body>
</html>
  `.trim();
}

// Create verification email text template (fallback for email clients that don't support HTML)
function createVerificationEmailText(email: string, verificationUrl: string): string {
  return `
Welcome to Hangman Game!

Hi there! Thanks for signing up for the Hangman Game. To complete your registration and start playing, please verify your email address by visiting this link:

${verificationUrl}

What's waiting for you:
- Daily word challenges with multiple difficulty levels
- 60-second time challenges  
- Smart hint system to help when you're stuck
- Compete on the global leaderboard
- Track your progress and win streaks

This verification link will expire in 24 hours.

If you didn't create an account with us, you can safely ignore this email.

This email was sent to ${email}
Hangman Game - Cooked with ❤️ by the development team
  `.trim();
}

// Send verification email using Resend API (or mock for development)
export async function sendVerificationEmail(email: string, token: string): Promise<boolean> {
  try {
    const verificationUrl = `${EMAIL_CONFIG.BASE_URL}/auth/verify-email?token=${token}`;

    // Use same environment detection as logger
    const isProduction = !!Deno.env.get("DENO_DEPLOYMENT_ID") || Deno.env.get("DENO_ENV") === "production";

    // Enhanced logging to track URL generation
    Logger.log(LogContext.AUTH, "GENERATING VERIFICATION URL", {
      baseUrl: EMAIL_CONFIG.BASE_URL,
      verificationUrl,
      isProduction,
      email,
      tokenPrefix: token.substring(0, 8) + "..."
    });

    // API key validation removed - the key format appears correct

    // Demo mode only when no API key is provided (regardless of environment)
    if (EMAIL_CONFIG.API_KEY === "demo-key") {
      Logger.log(LogContext.AUTH, "🔗 EMAIL VERIFICATION (Demo Mode - No API Key)", {
        to: email,
        token,
        verificationUrl,
        note: "Set RESEND_API_KEY environment variable to send real emails",
        tip: "For localhost testing: export RESEND_API_KEY=your_key && deno task dev"
      });
      return true;
    }

    // Skip API key validation - proceed directly to sending email
    // (The previous validation was failing because the API key is restricted to sending emails only)

    // Real email sending using Resend API
    Logger.log(LogContext.AUTH, "📤 Sending email via Resend API...");

    const emailPayload = {
      from: `${EMAIL_CONFIG.FROM_NAME} <${EMAIL_CONFIG.FROM_EMAIL}>`,
      to: [email],
      subject: "Verify your email - Hangman Game 🎮",
      html: createVerificationEmailHtml(email, verificationUrl),
      text: createVerificationEmailText(email, verificationUrl),
    };

    // Email payload debug logging removed

    const response = await fetch("https://api.resend.com/emails", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${EMAIL_CONFIG.API_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(emailPayload),
    });

    Logger.debug(LogContext.AUTH, "📬 Resend API response status:", { status: response.status });

    if (!response.ok) {
      const errorData = await response.text();
      Logger.error(LogContext.AUTH, "❌ Failed to send verification email:", new Error(`Status ${response.status}: ${errorData}`));
      return false;
    }

    const result = await response.json();
    Logger.log(LogContext.AUTH, "✅ Verification email sent successfully:", { id: result.id });
    return true;

  } catch (error) {
    Logger.error(LogContext.AUTH, "Error sending verification email:", error as Error);
    return false;
  }
}

// Validate email format (additional validation beyond the existing one)
export function isValidEmailFormat(email: string): boolean {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailRegex.test(email);
}

// Helper to extract domain from email
export function getEmailDomain(email: string): string {
  return email.split('@')[1]?.toLowerCase() || '';
}