// Simplified Auth Config - Only what's actually used

// Legacy AUTH_CONFIG export for rate limiting middleware
export const AUTH_CONFIG = {
  RATE_LIMIT_WINDOW_MS: 5 * 60 * 1000, // 5 minutes  
  RATE_LIMIT_MAX_REQ: 50, // 50 requests per 5 minutes
  SESSION_TTL_MS: 60 * 60 * 1000, // 1 hour
} as const;

// Legacy getRequestAuthConfig function for routes/auth.ts
export const getRequestAuthConfig = (req: Request) => {
  const url = new URL(req.url);
  const hostname = url.hostname;
  
  // Check if this is a production deployment (any deno.dev subdomain)
  if (hostname.includes('deno.dev')) {
    return {
      RP_ID: hostname, // Use the actual hostname as RP_ID
      RP_NAME: 'Hangman Game',
      ORIGIN: `https://${hostname}`,
    };
  }
  
  // Development/localhost
  return {
    RP_ID: 'localhost',
    RP_NAME: 'Hangman Game (Dev)',
    ORIGIN: `${url.protocol}//${url.host}`,
  };
};