// Simplified Auth Utils - Only what's actually used
import { Logger, LogContext } from "../utils/logger.ts";

// Base64URL encoding/decoding (used by legacy routes/auth.ts)
export const toB64 = (input: ArrayBuffer | Uint8Array): string => {
  const bytes = input instanceof Uint8Array ? input : new Uint8Array(input);
  const base64 = btoa(String.fromCharCode(...bytes));
  // Convert to base64url: replace + with -, / with _, and remove padding
  return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
};

export const fromB64 = (base64url: string): Uint8Array => {
  // Convert from base64url to base64: replace - with +, _ with /, and add padding
  let base64 = base64url.replace(/-/g, '+').replace(/_/g, '/');
  
  // Add padding if needed
  while (base64.length % 4) {
    base64 += '=';
  }
  
  return Uint8Array.from(atob(base64), c => c.charCodeAt(0));
};

// For functional auth routes that need Result type
export const fromBase64Url = (base64url: string): Uint8Array => fromB64(base64url);

// ID generation (used by kv.ts and routes/auth.ts)
export const generateSecureId = (length: number = 16): string => {
  const bytes = crypto.getRandomValues(new Uint8Array(length));
  return Array.from(bytes, byte => byte.toString(16).padStart(2, '0')).join('');
};

export const generateUserId = (): Uint8Array => {
  // Generate a 16-byte (128-bit) user ID for better mobile compatibility
  // Mobile browsers handle smaller user IDs more reliably
  const userId = crypto.getRandomValues(new Uint8Array(16));
  Logger.debug(LogContext.AUTH, "Generated new user ID:", { bytes: userId.length, preview: Array.from(userId.slice(0, 8)) });
  return userId;
};

// Utility to ensure user ID is a proper Uint8Array (for KV deserialization)
export const ensureUserIdUint8Array = (id: any): Uint8Array => {
  if (id instanceof Uint8Array) {
    return id;
  }
  
  // Reconstruct from object with numeric keys (KV storage artifact)
  if (typeof id === 'object' && id !== null) {
    const keys = Object.keys(id).map(Number).filter(n => !isNaN(n));
    if (keys.length > 0) {
      const length = Math.max(...keys) + 1;
      const reconstructed = new Uint8Array(length);
      for (const [key, value] of Object.entries(id)) {
        const numKey = parseInt(key);
        if (!isNaN(numKey)) {
          reconstructed[numKey] = value as number;
        }
      }
      Logger.debug(LogContext.AUTH, "Reconstructed user ID from object:", { bytes: reconstructed.length });
      return reconstructed;
    }
  }
  
  throw new Error(`Invalid user ID format: ${typeof id}`);
};

export const generateSessionId = (): string => generateSecureId(16); // 32 hex chars

// Time utility (used by kv.ts)
export const now = (): number => Date.now();