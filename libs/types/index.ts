/**
 * Centralized type exports for the entire application
 * Provides a single entry point for all shared types
 */

// Re-export all shared types
export * from "./shared.ts";

// Re-export game-specific types  
export * from "./game.ts";

// Re-export core types for convenience
export type { 
  RouteHandler, 
  AuthenticatedRouteHandler, 
  Route, 
  Router, 
  ApiResponse,
  LogLevel,
  LogContext,
  LogEntry,
  Environment,
  RateLimitResult
} from "../core/types.ts";

// Re-export auth types for convenience
export type { 
  User, 
  Session, 
  AuthState, 
  ValidationError 
} from "../auth/types.ts";

// Re-export utility types for convenience
export type { Result } from "../utils/result.ts";
export type { 
  ErrorType, 
  ErrorConfig, 
  ResponseFormat, 
  ErrorResponseOptions 
} from "../utils/errors.ts";
export type { 
  CookieConfig, 
  CookieOptions 
} from "../utils/cookies.ts";