/**
 * Shared types used across multiple modules to eliminate duplication
 * This module consolidates common type definitions used throughout the application
 */

// Import and re-export core types that are widely used
import type { 
  RouteHandler, 
  AuthenticatedRouteHandler, 
  Route, 
  Router, 
  ApiResponse,
  LogLevel,
  LogContext,
  LogEntry,
  Environment 
} from "../core/types.ts";

// Import and re-export auth types that may be used across modules
import type { 
  User, 
  Session, 
  AuthState, 
  ValidationError 
} from "../auth/types.ts";

export type { 
  RouteHandler, 
  AuthenticatedRouteHandler, 
  Route, 
  Router, 
  ApiResponse,
  LogLevel,
  LogContext,
  LogEntry,
  Environment,
  User, 
  Session, 
  AuthState, 
  ValidationError 
};

// Re-export utility types
export type { Result } from "../utils/result.ts";
export type { 
  ErrorType, 
  ErrorConfig, 
  ResponseFormat, 
  ErrorResponseOptions 
} from "../utils/errors.ts";
export type { 
  CookieConfig, 
  CookieOptions 
} from "../utils/cookies.ts";

/**
 * Common result types for handler operations
 */
export type HandlerResult<T> = {
  readonly ok: true;
  readonly value: T;
} | {
  readonly ok: false;
  readonly response: Response;
};

/**
 * Success/Error response pattern commonly used in APIs
 */
export type OperationResult<T = void, E = string> = {
  readonly success: true;
  readonly data: T;
} | {
  readonly success: false;
  readonly error: E;
};

/**
 * Generic pagination types for lists
 */
export interface PaginationOptions {
  readonly limit?: number;
  readonly offset?: number;
}

export interface PaginatedResponse<T> {
  readonly items: readonly T[];
  readonly total: number;
  readonly hasMore: boolean;
  readonly limit: number;
  readonly offset: number;
}

/**
 * Common timestamp interface for entities
 */
export interface Timestamped {
  readonly createdAt: number;
  readonly updatedAt?: number;
}

/**
 * Common ID interface for entities
 */
export interface Identifiable {
  readonly id: string;
}

/**
 * Request context that can be passed through middleware
 */
export interface RequestContext {
  readonly requestId: string;
  readonly timestamp: number;
  readonly userAgent?: string;
  readonly ip?: string;
  readonly authState?: AuthState;
}

/**
 * Configuration types
 */
export interface AppConfig {
  readonly port: number;
  readonly environment: Environment;
  readonly logLevel: LogLevel;
}

/**
 * Generic service response pattern
 */
export type ServiceResponse<T> = Promise<HandlerResult<T>>;

/**
 * File handling types
 */
export interface FileInfo {
  readonly path: string;
  readonly size: number;
  readonly mimeType: string;
  readonly lastModified: number;
}

/**
 * Common validation patterns
 */
export interface ValidationRule<T = any> {
  readonly field: keyof T;
  readonly validator: (value: any) => boolean | string;
  readonly message: string;
}

export interface ValidationResult {
  readonly valid: boolean;
  readonly errors: readonly ValidationError[];
}

/**
 * Event types for logging and monitoring
 */
export interface ApplicationEvent {
  readonly type: string;
  readonly timestamp: number;
  readonly data: Record<string, unknown>;
  readonly context?: LogContext;
}

/**
 * Cache-related types
 */
export interface CacheOptions {
  readonly ttl: number; // Time to live in seconds
  readonly key: string;
}

export interface CacheEntry<T> {
  readonly value: T;
  readonly expiry: number;
  readonly created: number;
}

/**
 * Common middleware types
 */
export type Middleware<T = any> = (
  request: Request,
  context: T
) => Promise<Response | void>;

export type MiddlewareStack<T = any> = readonly Middleware<T>[];