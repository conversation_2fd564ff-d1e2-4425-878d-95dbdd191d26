/**
 * Game-specific types used across the hangman application
 * Centralizes game domain types for better organization
 */

import type { Identifiable, Timestamped } from "./shared.ts";

/**
 * Core game domain types
 */
export type WordDifficulty = "easy" | "medium" | "hard";
export type GameStatus = "playing" | "won" | "lost";

/**
 * Game statistics interface
 */
export interface GameStatistics {
  readonly gamesPlayed: number;
  readonly gamesWon: number;
  readonly currentStreak: number;
  readonly bestStreak: number;
  readonly totalGuesses: number;
  readonly averageGuessesPerWin: number;
}

/**
 * Main game state interface
 */
export interface GameState extends Identifiable {
  readonly word: string;
  readonly guessedLetters: ReadonlySet<string>;
  readonly wrongGuesses: number;
  readonly maxWrong: number;
  readonly status: GameStatus;
  readonly difficulty: WordDifficulty;
  readonly category: string;
  readonly hintsUsed: number;
  readonly hintsAllowed: number;
  readonly startTime: number;
  readonly endTime: number | null;
  readonly timeLimit: number; // Time limit in seconds
  readonly statistics: GameStatistics;
  readonly username?: string;
  readonly winSequenceNumber?: number; // Mutable for adding after win detection
}

/**
 * Game operation result types
 */
export type GuessResult =
  | { readonly kind: "correct"; readonly letter: string }
  | { readonly kind: "incorrect"; readonly letter: string }
  | { readonly kind: "alreadyGuessed"; readonly letter: string }
  | { readonly kind: "hint"; readonly letter: string }
  | { readonly kind: "won"; readonly word: string }
  | { readonly kind: "lost"; readonly word: string };

export type HintResult =
  | { readonly kind: "revealed"; readonly letter: string }
  | { readonly kind: "noHintsLeft"; readonly hintsUsed: number; readonly hintsAllowed: number }
  | { readonly kind: "allLettersRevealed"; readonly word: string };

/**
 * Word and category management types
 */
export interface WordCategory {
  readonly name: string;
  readonly words: Record<WordDifficulty, readonly string[]>;
}

export interface WordListConfig {
  readonly categories: readonly WordCategory[];
  readonly defaultCategory: string;
  readonly defaultDifficulty: WordDifficulty;
}

/**
 * Session management types specific to game
 */
export interface GameSession {
  readonly sessionId: string;
  readonly gameState: GameState;
}

export type SessionValidationResult = GameSession;

/**
 * Game configuration types
 */
export interface GameConfig {
  readonly maxWrongGuesses: number;
  readonly hintsAllowed: number;
  readonly timeLimit: number; // seconds
  readonly defaultDifficulty: WordDifficulty;
  readonly defaultCategory: string;
  readonly defaultWordCount: number;
}

/**
 * Player and statistics types
 */
export interface PlayerStanding {
  readonly username: string;
  readonly displayName: string;
  readonly gamesWon: number;
  readonly averageDuration: number;
  readonly bestStreak: number;
  readonly rank: number;
}

export interface WinRecord extends Identifiable, Timestamped {
  readonly sequenceNumber: number;
  readonly username: string;
  readonly word: string;
  readonly completionTime: string;
  readonly duration: number;
  readonly totalGuesses: number;
  readonly hintsUsed: number;
  readonly completionMethod: "guess" | "hint";
  readonly difficulty: WordDifficulty;
  readonly category: string;
  readonly gameId: string;
}

/**
 * Daily game limit types
 */
export interface UserDailyGames {
  readonly username: string;
  readonly date: string; // YYYY-MM-DD format
  readonly gamesPlayed: number;
  readonly lastGameAt: number;
}

export interface DailyLimitInfo {
  readonly canPlay: boolean;
  readonly gamesPlayed: number;
  readonly gamesRemaining: number;
  readonly resetTime: number;
}

/**
 * Game UI/Display types
 */
export interface DisplayWord {
  readonly letters: readonly string[];
  readonly isComplete: boolean;
}

export interface GameDisplayState {
  readonly word: DisplayWord;
  readonly wrongGuesses: number;
  readonly maxWrong: number;
  readonly guessedLetters: readonly string[];
  readonly status: GameStatus;
  readonly timeRemaining: number;
  readonly hintsRemaining: number;
}

/**
 * Game event types for logging and analytics
 */
export type GameEventType = 
  | "game_started"
  | "letter_guessed" 
  | "hint_used"
  | "game_won"
  | "game_lost"
  | "time_expired";

export interface GameEvent {
  readonly type: GameEventType;
  readonly gameId: string;
  readonly username?: string;
  readonly timestamp: number;
  readonly data: Record<string, unknown>;
}

/**
 * API response types for game endpoints
 */
export interface GameApiResponse {
  readonly gameState: GameState;
  readonly sessionId: string;
  readonly message?: string;
}

export interface StandingsApiResponse {
  readonly standings: readonly PlayerStanding[];
  readonly currentUser?: {
    readonly rank: number;
    readonly stats: GameStatistics;
  };
}

export interface UserStatsApiResponse {
  readonly statistics: GameStatistics;
  readonly recentGames: readonly WinRecord[];
  readonly dailyLimit: DailyLimitInfo;
}