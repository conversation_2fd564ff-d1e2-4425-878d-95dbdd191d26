/**
 * Hangman application-specific constants
 */

// Game Configuration
export const GAME_CONFIG = {
  // Time limits in seconds
  TIME_LIMIT: 60,
  DEFAULT_TIME_LIMIT: 60,
  
  // Difficulty settings
  DEFAULT_DIFFICULTY: "hard",
  DEFAULT_CATEGORY: "Words",
  DEFAULT_WORD_COUNT: 1,
  
  // Daily limits
  DAILY_GAME_LIMIT: 10,
  
  // Hint system
  MAX_HINTS_PER_GAME: 3,
  
  // Display settings
  MAX_WORD_LENGTH: 20,
  MIN_WORD_LENGTH: 3
} as const;

// Hangman Session Configuration (app-specific)
export const HANGMAN_SESSION_CONFIG = {
  // Cookie settings
  COOKIE_NAME: 'hangman_session',
} as const;

// Hangman-specific CSS Class Names
export const CSS_CLASSES = {
  // Layout
  GAME_CONTAINER: 'game-container',
  GAME_HEADER: 'game-header',
  GAME_MAIN: 'game-main',
  GAME_NAV: 'game-nav',
  
  // Components
  HANGMAN_DISPLAY: 'hangman-display',
  WORD_DISPLAY: 'word-display',
  LETTER: 'letter',
  KEYBOARD: 'keyboard',
  
  // States
  STATUS_WIN: 'status win',
  STATUS_LOSE: 'status lose',
  GAME_OVER: 'game-over',
  VISIBLE: 'visible',
  ACTIVE: 'active',
  
  // Buttons
  BUTTON_CORRECT: 'correct',
  BUTTON_INCORRECT: 'incorrect',
  HINT_BUTTON: 'hint-button',
  NEW_GAME_NAV: 'new-game-nav',
  
  // Modals
  GAME_STATS: 'game-stats',
  PLAYER_STANDINGS_MODAL: 'player-standings-modal',
  
  // Notifications
  WELCOME_NOTIFICATION: 'welcome-notification',
  ERROR_MESSAGE: 'error-message',
  
  // Auth
  AUTH_CONTAINER: 'auth-container',
  AUTH_CARD: 'auth-card',
  USERNAME_INPUT: 'username-input',
  AUTH_BUTTON: 'auth-button',
  
  // Daily limit
  DAILY_LIMIT_CONTAINER: 'daily-limit-container',
  LIMIT_MESSAGE: 'limit-message',
  
  // Timer
  COUNTDOWN_TIMER: 'countdown-timer',
  COUNTDOWN_NUMBER: 'countdown-number',
  TIME_NORMAL: 'time-normal',
  TIME_WARNING: 'time-warning',
  TIME_CRITICAL: 'time-critical'
} as const;

// Hangman-specific UI Messages
export const MESSAGES = {
  // Game states
  GAME_WON: 'Congratulations! You won!',
  GAME_LOST: 'Game Over! Better luck next time.',
  TIME_EXPIRED: 'Time\'s up! Game over.',
  
  // Errors
  INVALID_LETTER: 'Invalid letter',
  NO_ACTIVE_GAME: 'No active game',
  NOT_AUTHENTICATED: 'Not authenticated',
  AUTHENTICATION_FAILED: 'Authentication failed',
  REGISTRATION_FAILED: 'Registration failed',
  
  // Daily limits
  DAILY_LIMIT_REACHED: 'Daily game limit reached',
  GAMES_REMAINING: 'games remaining today',
  
  // Loading states
  LOADING_STATS: 'Loading statistics...',
  LOADING_STANDINGS: 'Loading standings...',
  
  // Success messages
  STATS_UPDATED: 'Statistics updated successfully',
  STANDINGS_UPDATED: 'Standings updated successfully',
  
  // Validation
  EMAIL_REQUIRED: 'Please enter an email address'
} as const;

// Hangman API Endpoints
export const API_ENDPOINTS = {
  // Game endpoints
  GAME: '/',
  NEW_GAME: '/new-game',
  GUESS: '/guess',
  HINT: '/hint',
  TIME_EXPIRED: '/game/time-expired',
  
  // API endpoints
  DAILY_LIMIT_INFO: '/api/daily-limit-info',
  STANDINGS: '/api/standings',
  USER_STATS: '/api/user-stats',
  
  // Auth endpoints
  LOGIN: '/login',
  AUTH_LOGIN: '/auth/login',
  LOGOUT: '/auth/logout',
  
  // Static endpoints
  STATIC: '/static/*'
} as const;

// Hangman File Paths
export const FILE_PATHS = {
  // Static files
  STYLES_CSS: 'styles.css',
  KEYBOARD_JS: 'keyboard.js',
  
  // Templates
  LOGIN_PAGE: 'loginPage',
  HOME_PAGE: 'homePage',
  
  // Directories
  STATIC_DIR: './static',
  SRC_STATIC_DIR: './src/static',
  VIEWS_DIR: './src/views'
} as const;

// Game Difficulty Settings
export const DIFFICULTY = {
  EASY: 'easy',
  MEDIUM: 'medium',
  HARD: 'hard'
} as const;

// Game Categories
export const CATEGORIES = {
  WORDS: 'Words',
  ANIMALS: 'Animals',
  COUNTRIES: 'Countries',
  GENERAL: 'General'
} as const;

// Timer Constants
export const TIMER = {
  // Game timer thresholds
  WARNING_THRESHOLD: 20, // seconds
  CRITICAL_THRESHOLD: 10, // seconds
  
  // Update intervals
  TIMER_UPDATE_INTERVAL: 100, // milliseconds
  PERFORMANCE_CHECK_INTERVAL: 1000, // milliseconds
  
  // Animation durations
  ANIMATION_DURATION: 300, // milliseconds
  TRANSITION_SPEED: 300 // milliseconds
} as const;