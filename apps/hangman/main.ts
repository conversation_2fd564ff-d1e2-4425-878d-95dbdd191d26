// Hangman Game Application
// Entry point for the Hangman game using shared libraries

import * as effection from "jsr:@effection/effection";
import { createRouter } from "../../libs/core/router.ts";
import { 
  game<PERSON><PERSON><PERSON>, 
  new<PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  hint<PERSON><PERSON><PERSON>, 
  timeExpiredHandler, 
  dailyLimitInfoHandler, 
  standingsApiHandler, 
  userStatsApiHandler, 
  staticFileHandler 
} from "./routes/handlers.ts";
import { 
  adminList<PERSON><PERSON>s<PERSON>and<PERSON>, 
  adminGet<PERSON>ser<PERSON>andler, 
  adminRemoveUserHandler 
} from "./routes/adminHandlers.ts";
import { loginPage } from "./views/auth.ts";
import { rateLimit, securityHeaders } from "../../libs/middleware/rateLimiting.ts";
import { handleAuth } from "../../libs/auth/routes.ts";
import { requireAuth, SimpleAuthState } from "../../libs/auth/index.ts";
import { clearGameSession } from "./utils/session.ts";
import { <PERSON><PERSON><PERSON><PERSON>, AuthenticatedRoute<PERSON><PERSON><PERSON> } from "../../libs/core/types.ts";
import { Lo<PERSON>, LogContext } from "../../libs/utils/logger.ts";
import { cleanupOldDailyRecords } from "./utils/dailyGameLimit.ts";
import { BatchCookies } from "../../libs/utils/cookies.ts";

// ============================================================================
// Hangman App Integration Layer
// ============================================================================

type HangmanAuthHandler = AuthenticatedRouteHandler<SimpleAuthState & { isAuthenticated: true }>;

// Auth middleware using the shared auth system
const withAuth = (handler: HangmanAuthHandler): RouteHandler => {
  return async (request: Request, params: Record<string, string>): Promise<Response> => {
    const authResult = await requireAuth(request);
    
    if (authResult instanceof Response) {
      return authResult; // Redirect to login
    }
    
    if (!authResult.isAuthenticated) {
      return new Response(null, {
        status: 302,
        headers: { "Location": "/login" },
      });
    }
    
    return handler(request, params, authResult as SimpleAuthState & { isAuthenticated: true });
  };
};

// Custom logout handler that cleans up game session
const customLogoutHandler = async (req: Request, params: Record<string, string>): Promise<Response> => {
  // First clear the game session
  const gameSessionCookie = clearGameSession(req);
  
  // Then handle the auth logout
  const authResponse = await handleAuth(req);
  
  // Combine both session clearing cookies using centralized utility
  const authCookies = authResponse.headers.get("Set-Cookie") || "";
  const combinedCookies = BatchCookies.combine(authCookies, gameSessionCookie);
  
  // Create new response with both cookies
  const newResponse = new Response(authResponse.body, {
    status: authResponse.status,
    statusText: authResponse.statusText,
    headers: {
      ...Object.fromEntries(authResponse.headers.entries()),
      "Set-Cookie": combinedCookies
    }
  });
  
  Logger.log(LogContext.AUTH, "Custom logout: cleared both auth and game sessions");
  return newResponse;
};

// ============================================================================
// Hangman Server Setup
// ============================================================================

const createHangmanServer = () => {
  Logger.log(LogContext.SERVER, "🔧 Setting up Hangman server...");
  
  // Create protected route handlers using shared auth middleware
  const protectedGameHandler = withAuth(gameHandler);
  const protectedNewGameHandler = withAuth((req, _params, auth) => newGameHandler(req, auth));
  const protectedGuessHandler = withAuth(guessHandler);
  const protectedHintHandler = withAuth(hintHandler);
  const protectedTimeExpiredHandler = withAuth(timeExpiredHandler);
  const protectedDailyLimitInfoHandler = withAuth((req, _params, auth) => dailyLimitInfoHandler(req, _params, auth));
  const protectedStandingsApiHandler = withAuth((req, params, auth) => standingsApiHandler(req, auth));
  const protectedUserStatsApiHandler = withAuth((req, params, auth) => userStatsApiHandler(req, auth));
  
  // Auth route handlers using shared auth
  const loginHandler = async (request: Request, params: Record<string, string>): Promise<Response> => {
    return new Response(loginPage(), {
      headers: { "Content-Type": "text/html" },
    });
  };
  
  Logger.log(LogContext.SERVER, "✅ Hangman app ready");
  
  return {
    router: createRouter([
      { path: "/", handler: protectedGameHandler },
      { path: "/new-game", handler: protectedNewGameHandler },
      { path: "/guess/:letter", handler: protectedGuessHandler },
      { path: "/hint", handler: protectedHintHandler },
      { path: "/game/time-expired", handler: protectedTimeExpiredHandler },
      { path: "/api/daily-limit-info", handler: protectedDailyLimitInfoHandler },
      { path: "/api/standings", handler: protectedStandingsApiHandler },
      { path: "/api/user-stats", handler: protectedUserStatsApiHandler },
      { path: "/login", handler: loginHandler },
      { path: "/auth/login", handler: (req: Request, params: Record<string, string>) => handleAuth(req) },
      { path: "/auth/logout", handler: customLogoutHandler },
      { path: "/admin/kv/stats", handler: adminKvStatsHandler },
      { path: "/admin/kv/clear", handler: adminKvClearHandler },
      { path: "/admin/users", handler: adminListUsersHandler },
      { path: "/admin/users/:username", handler: adminGetUserHandler },
      { path: "/admin/users/:username/remove", handler: adminRemoveUserHandler },
      { path: "/static/*", handler: staticFileHandler },
    ]),
  };
};

// ============================================================================
// Admin Handlers for KV Management
// ============================================================================

// Simple admin auth check using environment variable
function checkAdminAuth(request: Request): boolean {
  const adminKey = Deno.env.get("ADMIN_KEY");
  if (!adminKey) return false;
  
  const auth = request.headers.get("Authorization");
  return auth === `Bearer ${adminKey}`;
}

// Admin KV stats handler
const adminKvStatsHandler = async (request: Request): Promise<Response> => {
  if (!checkAdminAuth(request)) {
    return new Response("Unauthorized", { status: 401 });
  }
  
  try {
    // Import and run the stats functionality
    const { getKvStats } = await import("./scripts/kv-utils.ts");
    const stats = await getKvStats();
    
    return new Response(JSON.stringify(stats, null, 2), {
      headers: { "Content-Type": "application/json" }
    });
  } catch (error) {
    Logger.error(LogContext.SERVER, "Admin KV stats error", error as Error);
    return new Response("Internal server error", { status: 500 });
  }
};

// Admin KV clear handler
const adminKvClearHandler = async (request: Request): Promise<Response> => {
  if (!checkAdminAuth(request)) {
    return new Response("Unauthorized", { status: 401 });
  }
  
  const url = new URL(request.url);
  const type = url.searchParams.get("type") || "all";
  const confirm = url.searchParams.get("confirm") === "true";
  
  if (!confirm) {
    return new Response(JSON.stringify({
      error: "Missing confirmation",
      message: "Add ?confirm=true to proceed with clearing data",
      availableTypes: ["all", "users", "games", "sessions"]
    }), {
      status: 400,
      headers: { "Content-Type": "application/json" }
    });
  }
  
  try {
    // Import and run the clear functionality
    const { clearKvData } = await import("./scripts/kv-utils.ts");
    const result = await clearKvData(type);
    
    return new Response(JSON.stringify({
      success: true,
      message: `Cleared ${type} data`,
      deletedCount: result.deletedCount
    }), {
      headers: { "Content-Type": "application/json" }
    });
  } catch (error) {
    Logger.error(LogContext.SERVER, "Admin KV clear error", error as Error);
    return new Response("Internal server error", { status: 500 });
  }
};

// ============================================================================
// Signal Handlers and Server Management
// ============================================================================

function setupSignalHandlers(cb: () => void) {
  try {
    Deno.addSignalListener("SIGINT", () => {
      Logger.log(LogContext.SERVER, "\\nReceived SIGINT signal");
      cb();
    });
  } catch (error) {
    Logger.error(LogContext.SERVER, "Failed to setup signal handlers:", error);
  }
}

// ============================================================================
// Main Hangman Server Function
// ============================================================================

const runHangmanServer = function* () {
  try {
    // Initialize the hangman server
    Logger.log(LogContext.SERVER, "🚀 Starting Hangman game server...");
    const serverComponents = yield* effection.call(createHangmanServer);
    const { router } = serverComponents;
    
    const controller = new AbortController();
    const signal = controller.signal;
    
    // Setup signal handlers for graceful shutdown
    setupSignalHandlers(() => {
      Logger.log(LogContext.SERVER, "🛑 Gracefully shutting down Hangman server...");
      controller.abort();
    });
    
    // Start the HTTP server
    const port = Deno.env.get("PORT") ? parseInt(Deno.env.get("PORT") || "8001") : 8001;
    Logger.log(LogContext.SERVER, `🎮 Hangman game server running on port ${port}`);
    Logger.log(LogContext.SERVER, `📡 Access the game at: http://localhost:${port}`);
    
    const server = Deno.serve({ port, signal }, async (req: Request) => {
      const url = new URL(req.url);
      const path = url.pathname;
      
      try {
        let rateLimitHeaders: Record<string, string> | undefined;
        
        // Apply rate limiting to auth routes using the shared system
        if (path.startsWith("/auth/")) {
          const rateLimitResult = await rateLimit(req);
          if (rateLimitResult.response) {
            return await securityHeaders(rateLimitResult.response);
          }
          rateLimitHeaders = rateLimitResult.rateLimitHeaders;
        }
        
        const response = await router(req, path);
        return await securityHeaders(response, rateLimitHeaders);
      } catch (error) {
        Logger.error(LogContext.SERVER, "❌ Hangman server error:", error);
        const errorResponse = new Response("Server error", { status: 500 });
        return await securityHeaders(errorResponse);
      }
    });
    
    // Create a monitoring task for the abort signal
    yield* effection.spawn(function* () {
      try {
        while (true) {
          if (signal.aborted) break;
          yield* effection.sleep(100);
        }
        Logger.log(LogContext.SERVER, "⚠️ Abort signal detected, cleaning up...");
      } catch (error) {
        Logger.error(LogContext.SERVER, "❌ Error in signal monitor:", error);
      }
    });
    
    // Create a daily cleanup task
    yield* effection.spawn(function* () {
      try {
        while (!signal.aborted) {
          // Wait 24 hours (24 * 60 * 60 * 1000 ms)
          yield* effection.sleep(24 * 60 * 60 * 1000);
          
          Logger.log(LogContext.GAME, "🧹 Running daily cleanup...");
          try {
            yield* effection.call(() => cleanupOldDailyRecords());
          } catch (error) {
            Logger.error(LogContext.GAME, "❌ Daily cleanup failed:", error);
          }
        }
      } catch (error) {
        Logger.error(LogContext.SERVER, "❌ Error in daily cleanup task:", error);
      }
    });
    
    // Wait for server completion or abort
    while (!signal.aborted) {
      yield* effection.sleep(1000);
    }
    
    // Server cleanup
    try {
      server.shutdown();
      Deno.removeSignalListener("SIGINT", () => { });
      Logger.log(LogContext.SERVER, "🧹 Hangman server cleanup completed");
    } catch (error) {
      Logger.error(LogContext.SERVER, "❌ Error during cleanup:", error);
    }
    
  } catch (error) {
    Logger.error(LogContext.SERVER, "💥 Critical Hangman server error:", error);
  } finally {
    Logger.log(LogContext.SERVER, "🏁 Hangman server has been shut down");
  }
};

// ============================================================================
// Entry Point
// ============================================================================

// Run the hangman server with effection
if (import.meta.main) {
  Logger.log(LogContext.SERVER, "🎯 Starting Hangman Game Application");
  effection.main(runHangmanServer);
}

// Export for Deno Deploy
export default runHangmanServer;