/**
 * Re-export game types from centralized type definitions
 * This maintains backward compatibility while using shared types
 */
export type {
  WordDifficulty,
  GameStatus,
  GameStatistics,
  GameState,
  GuessResult,
  HintResult,
  WordCategory,
  GameSession,
  SessionValidationResult,
  GameConfig,
  PlayerStanding,
  WinRecord,
  UserDailyGames,
  DailyLimitInfo,
  GameDisplayState,
  GameEvent,
  GameEventType,
  GameApiResponse,
  StandingsApiResponse,
  UserStatsApiResponse
} from "../../libs/types/game.ts";