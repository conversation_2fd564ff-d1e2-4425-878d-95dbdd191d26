import { getCategoryByName } from "../data/wordLists.ts";
import { WordDifficulty, GameState } from "../types.ts";
import { Result, ok, err } from "../../../libs/utils/result.ts";
import { Logger, LogContext } from "../../../libs/utils/logger.ts";
import { getAvailableWords, addUsedWord } from "../utils/wordTracking.ts";
import { validateGameAccess, incrementDailyGameCount } from "../utils/dailyGameLimit.ts";
import { calculateWinStatistics, calculateLossStatistics } from "../utils/statisticsCalculations.ts";
import { match } from "../../../libs/utils/pattern.ts";

/**
 * Select a random word from the appropriate word list, excluding previously used words
 */
export const selectRandomWord = async (
  difficulty: WordDifficulty, 
  category: string = "General",
  username?: string
): Promise<Result<string, Error>> => {
  const wordCategory = getCategoryByName(category);
  const allWords = wordCategory.words[difficulty];

  if (!allWords || allWords.length === 0) {
    return err(new Error(`No words available for difficulty level: ${difficulty} in category: ${category}`));
  }

  let availableWords: string[];
  
  if (username) {
    // Get words that haven't been used by this user
    try {
      availableWords = await getAvailableWords(username, category, allWords);
    } catch (error) {
      Logger.warn(LogContext.GAME, "Failed to get word history, using all words", { username, error: error instanceof Error ? error.message : String(error) });
      availableWords = [...allWords];
    }
  } else {
    // No user tracking for anonymous players
    availableWords = [...allWords];
  }

  if (availableWords.length === 0) {
    return err(new Error(`No available words for difficulty level: ${difficulty} in category: ${category}`));
  }

  // Type-safe access with runtime validation
  const word = availableWords[Math.floor(Math.random() * availableWords.length)];
  const result = typeof word === "string"
    ? ok(word)
    : err(new Error(`Expected string, got ${typeof word}`));

  // Track the used word for authenticated users
  if (result.ok && username) {
    try {
      await addUsedWord(username, category, result.value);
    } catch (error) {
      Logger.warn(LogContext.GAME, "Failed to track used word", { username, word: result.value, error: error instanceof Error ? error.message : String(error) });
    }
  }

  return result;
};

/**
 * Create initial game state
 */
export const createGame = async (
  difficulty: WordDifficulty = "hard",
  category: string = "Words",
  hintsAllowed: number = 1,
  username?: string
): Promise<Result<GameState, Error>> => {
  // Check daily game limit for authenticated users
  if (username) {
    try {
      const accessCheck = await validateGameAccess(username);
      if (!accessCheck.canPlay) {
        return err(new Error(`DAILY_LIMIT_REACHED:${accessCheck.gamesPlayed}:${accessCheck.gamesRemaining}`));
      }
    } catch (error) {
      Logger.warn(LogContext.GAME, "Failed to check daily limit, allowing game", { 
        username, 
        error: error instanceof Error ? error.message : String(error) 
      });
    }
  }

  const wordResult = await selectRandomWord(difficulty, category, username);

  if (!wordResult.ok) {
    return wordResult;
  }

  // Load existing statistics for the user if username is provided
  let statistics = {
    gamesPlayed: 0,
    gamesWon: 0,
    currentStreak: 0,
    bestStreak: 0,
    totalGuesses: 0,
    averageGuessesPerWin: 0
  };

  if (username) {
    try {
      const { getUserStatistics } = await import("../../../libs/auth/kv.ts");
      statistics = await getUserStatistics(username);
    } catch (error) {
      Logger.warn(LogContext.GAME, "Failed to load user statistics", { username, error: error instanceof Error ? error.message : String(error) });
      // Use default statistics if loading fails
    }
  }

  const gameState = {
    id: crypto.randomUUID(),
    word: wordResult.value,
    guessedLetters: new Set<string>(),
    wrongGuesses: 0,
    maxWrong: 7,
    status: "playing" as const,
    difficulty,
    category,
    hintsUsed: 0,
    hintsAllowed,
    startTime: Date.now(),
    endTime: null,
    timeLimit: 60, // 30 seconds per game
    statistics,
    username
  };

  // Increment daily game count for authenticated users
  if (username) {
    try {
      await incrementDailyGameCount(username);
    } catch (error) {
      Logger.warn(LogContext.GAME, "Failed to increment daily game count", { 
        username, 
        error: error instanceof Error ? error.message : String(error) 
      });
    }
  }

  return ok(gameState);
};

/**
 * Check if game time has expired
 */
export const checkTimeExpired = (state: GameState): boolean => {
  if (state.status !== "playing") {
    return false;
  }
  
  const elapsedTime = (Date.now() - state.startTime) / 1000; // in seconds
  return elapsedTime >= state.timeLimit;
};

/**
 * Create a new game state with time expired (lost status)
 */
export const createTimeExpiredState = (state: GameState): GameState => {
  const endTime = Date.now();
  const totalGuesses = state.guessedLetters.size;
  
  // Use centralized statistics calculation for consistency
  const statistics = calculateLossStatistics(state.statistics, totalGuesses);

  return {
    ...state,
    status: "lost" as const,
    endTime,
    statistics
  };
};

/**
 * Validate a guess input
 */
const validateGuess = (letter: string): Result<string, Error> => {
  if (!letter) {
    return err(new Error("No letter provided"));
  }

  const normalizedLetter = letter.toUpperCase();
  if (!/^[A-Z]$/.test(normalizedLetter)) {
    return err(new Error(`Invalid letter: ${letter}`));
  }

  return ok(normalizedLetter);
};

/**
 * Process a guess, returning the updated game state
 */
export const processGuess = (state: GameState, letter: string): Result<GameState, Error> => {
  // If game is not playing, return state unchanged
  if (state.status !== "playing") {
    return ok(state);
  }

  // Validate the letter
  const validatedLetter = validateGuess(letter);
  if (!validatedLetter.ok) {
    return validatedLetter;
  }

  // Already guessed this letter
  if (state.guessedLetters.has(validatedLetter.value)) {
    return ok(state);
  }

  // Create new set with the guessed letter added
  const newGuessedLetters = new Set(state.guessedLetters);
  newGuessedLetters.add(validatedLetter.value);

  // Calculate new wrong guesses
  const newWrongGuesses =
    state.word.includes(validatedLetter.value)
      ? state.wrongGuesses
      : state.wrongGuesses + 1;

  // Determine if the word is complete
  const isWordGuessed = [...state.word].every(l => newGuessedLetters.has(l));

  // Determine new game status
  const newStatus =
    isWordGuessed
      ? "won" as const
      : newWrongGuesses >= state.maxWrong
        ? "lost" as const
        : "playing" as const;

  // Calculate game statistics using centralized functions
  const totalGuesses = state.guessedLetters.size + 1; // Include this guess

  // Create base state update that's common to all outcomes
  const baseStateUpdate = {
    ...state,
    guessedLetters: newGuessedLetters,
    wrongGuesses: newWrongGuesses,
    status: newStatus,
  };

  // Handle state transitions using pattern matching for cleaner logic
  return match(newStatus)
    .with("won", () => {
      const endTime = Date.now();
      const statistics = calculateWinStatistics(state.statistics, totalGuesses);
      return ok({
        ...baseStateUpdate,
        endTime,
        statistics
      });
    })
    .with("lost", () => {
      const endTime = Date.now();
      const statistics = calculateLossStatistics(state.statistics, totalGuesses);
      return ok({
        ...baseStateUpdate,
        endTime,
        statistics
      });
    })
    .with("playing", () => ok(baseStateUpdate))
    .exhaustive();
};

/**
 * Get word with only guessed letters revealed
 */
export const getDisplayWord = (state: GameState): Result<string[], Error> => {
  if (!state || !state.word) {
    return err(new Error("Invalid game state"));
  }

  return ok([...state.word].map(letter =>
    state.guessedLetters.has(letter) ? letter : ""));
};

/**
 * Get a hint (reveal an unguessed letter)
 */
export const getHint = (state: GameState): Result<GameState, Error> => {
  // If game is not playing, return state unchanged
  if (state.status !== "playing") {
    return ok(state);
  }

  // Check if hints are available
  if (state.hintsUsed >= state.hintsAllowed) {
    return ok(state); // No hints left, return unchanged state
  }

  // Find unguessed letters in the word
  const unguessedLetters = [...state.word].filter(letter => !state.guessedLetters.has(letter));

  // If all letters are already guessed, return unchanged state
  if (unguessedLetters.length === 0) {
    return ok(state);
  }

  // Select a random unguessed letter
  const hintLetter = unguessedLetters[Math.floor(Math.random() * unguessedLetters.length)];

  // Create new set with the hint letter added
  const newGuessedLetters = new Set(state.guessedLetters);
  newGuessedLetters.add(hintLetter);

  // Determine if the word is complete after adding the hint
  const isWordGuessed = [...state.word].every(l => newGuessedLetters.has(l));

  // Determine new game status
  const newStatus = isWordGuessed ? "won" as const : "playing" as const;

  // Calculate game statistics if game is won using centralized function
  let statistics = state.statistics;
  let endTime = null;

  if (newStatus === "won") {
    endTime = Date.now();
    const totalGuesses = state.guessedLetters.size + 1; // Include this hint as a guess
    statistics = calculateWinStatistics(state.statistics, totalGuesses);
  }

  // Return new immutable state
  return ok({
    ...state,
    guessedLetters: newGuessedLetters,
    hintsUsed: state.hintsUsed + 1,
    status: newStatus,
    endTime,
    statistics
  });
};