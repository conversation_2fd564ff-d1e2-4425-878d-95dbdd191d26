/* COMPLETELY DISABLE ALL ANIMATIONS AND TRANSITIONS */
*, *::before, *::after {
  animation: none !important;
  animation-duration: 0s !important;
  animation-delay: 0s !important;
  transition: none !important;
  transition-duration: 0s !important;
  transition-delay: 0s !important;
  transform: none !important;
}

:root {
  --primary-color: #2c3e50;
  --menu-item-color: #2ecc7152;
  --secondary-color: #3498db;
  --success-color: #2ecc71;
  --danger-color: #e74c3c;
  --text-color: #34495e;

  /* Responsive sizing variables */
  --base-padding: clamp(0.5rem, 3vw, 1.2rem);
  --font-size-h1: clamp(1.75rem, 5vw, 2.5rem);
  --font-size-word: clamp(1.25rem, 4vw, 2.5rem);
  --font-size-btn: clamp(0.9rem, 2.5vw, 1.1rem);
  --letter-spacing: clamp(0.2rem, 1vw, 0.5rem);
  --button-size: clamp(32px, 8vw, 40px);
  --svg-width: clamp(220px, 70vw, 300px);
}

body {
  margin: 0;
  padding: 1rem;
  height: 100vh;
  min-height: 600px; /* Minimum height to prevent cramping */
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  overflow-y: auto;
}

header {
  text-align: center;
  margin-bottom: 5px;
  position: relative;
  z-index: 0;
  pointer-events: none;
  align-self: stretch;
}

h1 {
  color: rgba(44, 62, 80, 0.8);
  margin: 0;
  font-size: calc(var(--font-size-h1) * 1.5);
  line-height: 1.2;
  font-weight: 800;
  letter-spacing: -0.05em;
  text-transform: uppercase;
  text-shadow:
    0 5px 10px rgba(0, 0, 0, 0.15),
    0 2px 4px rgba(0, 0, 0, 0.2),
    2px 2px 0px #4a6fa5,
    -2px -2px 0px #c3cfe2;
  position: relative;
  display: inline-block;
}

.description {
  color: #666;
  max-width: 600px;
  line-height: 1.6;
  margin: 0 auto 2rem;
}

.game-container {
  width: min(100%, 800px);
  height: min(100%, calc(100vh - 2rem));
  min-height: 500px; /* Minimum height to prevent cramping */
  max-height: calc(100vh - 2rem);
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 1rem;
  box-shadow:
    0 10px 20px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  margin: 0;
  backdrop-filter: blur(5px);
  display: flex;
  flex-direction: column;
  align-items: stretch;
  overflow-y: auto;
}

.hangman-display {
  position: relative;
  width: min(var(--svg-width), 240px); /* Cap the maximum width */
  max-height: 180px; /* Limit height to preserve space */
  margin: 0 auto;
  background: transparent !important;
  background-color: transparent !important;
}

svg {
  width: 100%;
  transform-origin: center;
  background: transparent;
}

.hangman-figure,
.celebrate-figure {
  background: transparent;
}

.hangman-part {
  stroke: var(--primary-color);
  stroke-width: 4;
  stroke-linecap: round;
  fill: transparent;
  opacity: 0;
}

.hangman-part.visible {
  opacity: 1;
}


/* Word display */
.word-display {
  display: flex;
  justify-content: center;
  gap: clamp(0.3rem, 2vw, 0.8rem);
  margin: 0.5rem 0;
  font-size: clamp(1.2rem, 3.5vw, 2rem); /* More responsive scaling */
  letter-spacing: clamp(0.2rem, 0.8vw, 0.4rem);
  color: var(--text-color);
  flex-wrap: wrap;
  flex-shrink: 0; /* Don't shrink */
}

.letter {
  border-bottom: 4px solid var(--secondary-color);
  min-width: 1em;
  text-align: center;
  text-transform: uppercase;
}

/* Status display */
.status {
  text-align: center;
  margin: 0.5rem 0;
  padding: 0.25rem 0;
  font-size: 1.1rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0; /* Don't shrink */
}

.status.win {
  color: var(--success-color);
  font-weight: bold;
  text-align: center;
}

.status.lose {
  color: var(--danger-color);
  font-weight: bold;
}

/* Keyboard */
.keyboard-container {
  flex: 0 0 auto; /* Don't grow, just take needed space */
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  min-height: 140px; /* Minimum space for keyboard */
}

/* Defensive CSS: Ensure keyboard buttons are never affected by navigation disabled state */
.keyboard-container button {
  pointer-events: auto !important;
}

.keyboard {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(var(--button-size), 1fr));
  gap: clamp(0.25rem, 1vw, 0.5rem);
  margin-top: clamp(0.5rem, 2vw, 1rem);
}

.keyboard.game-over {
  opacity: 0.7;
}

button {
  padding: clamp(0.4rem, 2vw, 0.8rem);
  border: none;
  border-radius: 0.5rem;
  background: var(--secondary-color);
  color: white;
  font-size: var(--font-size-btn);
  cursor: pointer;
  min-height: var(--button-size);
  touch-action: manipulation;
}

button:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  opacity: 0.7;
}

button.correct {
  background: var(--success-color);
  color: white;
  opacity: 0.8;
}

button.incorrect {
  background: var(--danger-color);
  color: white;
  opacity: 0.8;
}

button:hover:not(:disabled) {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

button:active:not(:disabled) {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}


/* Game header and navigation */
.game-header {
  width: 100%;
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(52, 152, 219, 0.2);
  gap: 1rem;
}

.game-title h2 {
  margin: 0;
  color: var(--primary-color);
  font-size: clamp(1.2rem, 3vw, 1.5rem);
}

.game-nav {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  justify-self: end;
}

/* Main game area */
.game-main {
  width: 100%;
  flex: 0 0 auto; /* Don't grow, let keyboard have space */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: 0.5rem;
  position: relative;
  max-height: 60vh; /* Limit to 60% of viewport height */
}

/* Category dropdown */
.category-dropdown {
  position: relative;
}

.category-dropdown select {
  padding: 0.4rem 0.8rem;
  border-radius: 2rem;
  border: 1px solid var(--secondary-color);
  background-color: white;
  color: var(--secondary-color);
  font-family: inherit;
  font-size: 0.9rem;
  cursor: pointer;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;utf8,<svg fill='%233498db' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/><path d='M0 0h24v24H0z' fill='none'/></svg>");
  background-repeat: no-repeat;
  background-position: right 0.5rem center;
  background-size: 1.2rem;
  padding-right: 2rem;
}

.category-dropdown select:hover {
  border-color: #2980b9;
  box-shadow: 0 2px 5px rgba(52, 152, 219, 0.2);
}

.category-dropdown select:focus {
  outline: none;
  border-color: #2980b9;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
}

/* Navigation buttons - base styling */
.dashboard-toggle,
.standings-button,
.new-game-nav,
.logout-button {
  color: white;
  border: none;
  border-radius: 50%;
  width: 2.2rem;
  height: 2.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  outline: none;
}

/* Individual button backgrounds */
.dashboard-toggle,
.standings-button,
.logout-button {
  background: var(--primary-color);
}

/* Dashboard (Stats) button specific styling */
.dashboard-toggle {
  background: var(--menu-item-color);
}

.dashboard-toggle:hover:not(.nav-btn-active) {
  background: #27ae60;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Standings button specific styling */
.standings-button {
  background: var(--menu-item-color);
}

.standings-button:hover:not(.nav-btn-active) {
  background: #27ae60;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Logout button hover */
.logout-button:hover {
  background: #3a506b;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Remove browser default focus styles */
.dashboard-toggle:focus,
.standings-button:focus,
.new-game-nav:focus,
.logout-button:focus {
  outline: none;
}

/* New Game button specific styling */
.new-game-nav {
  background: var(--menu-item-color);
}

.new-game-nav:hover:not(:disabled):not(.nav-btn-active) {
  background: #27ae60;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.new-game-nav:disabled,
.new-game-nav.disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  opacity: 0.6;
}

.new-game-nav:disabled:hover,
.new-game-nav.disabled:hover {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.dashboard-icon {
  font-size: 1.2rem;
}

/* Bulletproof active state for nav buttons - overrides everything */
.nav-btn-active {
  background: #27ae60 !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

/* Disabled nav buttons during active gameplay */
.game-nav .nav-btn-disabled {
  background: #bdc3c7 !important;
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
  pointer-events: none !important;
}

/* No hover effects for disabled nav buttons */
.game-nav .nav-btn-disabled:hover {
  background: #bdc3c7 !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
  opacity: 0.5 !important;
}

/* Inline new game icon styling - matches navbar button */
.new-game-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: var(--success-color);
  color: white;
  border-radius: 50%;
  width: 1.4rem;
  height: 1.4rem;
  font-size: 0.8rem;
  vertical-align: middle;
  margin: 0 0.2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Countdown timer - now positioned above hangman */
.countdown-timer {
  text-align: center;
  padding: 0.5rem 0;
  width: 40%;
  border-radius: 8px;
  flex-shrink: 0; /* Don't shrink */
}

.countdown-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.1rem;
}

.countdown-number {
  font-size: 1.8rem;
  font-weight: 700;
  font-family: 'Courier New', monospace;
  min-width: 2.5rem;
  text-align: center;
  line-height: 1;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.countdown-number.time-normal {
  color: #9ca3af; /* Gray for 30-21 seconds */
}

.countdown-number.time-warning {
  color: #1f2937; /* Black for 20-11 seconds */
}

.countdown-number.time-critical {
  color: #dc2626; /* Red for 10-0 seconds */
}

/* Make the entire timer more dramatic when critical */
.countdown-timer:has(.countdown-number.time-critical) {
  background: rgba(220, 38, 38, 0.15);
  border-color: rgba(220, 38, 38, 0.3);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.2);
}

.countdown-label {
  font-size: 0.7rem;
  color: #6b7280;
  font-weight: 500;
  line-height: 1;
}


/* Player standings */
.player-standings {
  margin: 1rem 0;
  padding: 1rem;
  border-radius: 0.5rem;
  background: rgba(46, 204, 113, 0.05);
  border: 1px solid rgba(46, 204, 113, 0.1);
  width: 100%;
  max-width: 600px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}


.player-standings h3 {
  margin: 0;
  color: var(--primary-color);
  text-align: center;
  font-size: 1.3rem;
}

.standings-header {
  display: grid;
  grid-template-columns: 60px 1fr 80px 90px;
  gap: 0.5rem;
  padding: 0.5rem;
  background: rgba(46, 204, 113, 0.1);
  border-radius: 0.25rem;
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.standings-list {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.standing-row {
  display: grid;
  grid-template-columns: 60px 1fr 80px 90px;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 0.25rem;
  background: rgba(255, 255, 255, 0.5);
  align-items: center;
}

.standing-row:hover {
  background: rgba(46, 204, 113, 0.1);
}

.standing-row.current-user {
  background: rgba(52, 152, 219, 0.15);
  border: 1px solid rgba(52, 152, 219, 0.3);
  font-weight: 600;
}

.standing-row.current-user:hover {
  background: rgba(52, 152, 219, 0.2);
}

.rank {
  text-align: center;
  font-weight: 600;
  color: var(--primary-color);
}

.player-name {
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.wins {
  text-align: center;
  font-weight: 600;
  color: var(--primary-color);
}

.avg-time {
  text-align: center;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.standings-empty {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
  font-style: italic;
}

.standings-note {
  text-align: center;
  margin-top: 0.75rem;
  color: #6b7280;
}

/* Daily limit reached */
.daily-limit-container {
  width: min(100%, 600px);
  height: 100%;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.limit-message {
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  border: 2px solid #ffc107;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 8px 20px rgba(255, 193, 7, 0.3);
  text-align: center;
}

.limit-message h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: #856404;
  font-size: 1.8rem;
}

.limit-details {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin: 1.5rem 0;
  flex-wrap: wrap;
}

.games-played,
.games-total {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.limit-number {
  font-size: 3rem;
  font-weight: 700;
  color: #dc3545;
  font-family: 'Courier New', monospace;
}

.games-total .limit-number {
  color: #856404;
}

.limit-label {
  font-size: 0.9rem;
  color: #6c757d;
  font-weight: 500;
}

.limit-separator {
  font-size: 1.2rem;
  color: #6c757d;
  font-weight: 600;
}

.reset-info {
  margin: 1.5rem 0;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 0.5rem;
}

.reset-info p {
  margin: 0.5rem 0;
  color: #495057;
}

.reset-time {
  font-weight: 600;
  color: #28a745;
}

.limit-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin: 1.5rem 0;
  flex-wrap: wrap;
}

.standings-link-button,
.logout-link-button {
  background: var(--secondary-color);
  color: white;
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  font-weight: 600;
}

.logout-link-button {
  background: #6c757d;
}

.standings-link-button:hover {
  background: #2980b9;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.logout-link-button:hover {
  background: #5a6268;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.limit-note {
  margin-top: 1rem;
  color: #6c757d;
  font-style: italic;
}

/* Games remaining indicator */
.games-remaining {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem;
  background: rgba(40, 167, 69, 0.1);
  border: 1px solid rgba(40, 167, 69, 0.3);
  border-radius: 0.5rem;
  margin: 0.5rem 0;
}

.remaining-count {
  font-size: 1.5rem;
  font-weight: 700;
  color: #28a745;
  font-family: 'Courier New', monospace;
}

.remaining-label {
  font-size: 0.8rem;
  color: #6c757d;
  font-weight: 500;
}

/* Daily limit display in stats */
.daily-limit-info {
  margin: 1rem 0;
  padding: 0.75rem;
  background: rgba(40, 167, 69, 0.05);
  border: 1px solid rgba(40, 167, 69, 0.2);
  border-radius: 0.5rem;
}

.daily-limit-display {
  text-align: center;
}

.limit-counter {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.games-left {
  font-size: 1rem;
  font-weight: 700;
  color: #28a745;
  font-family: 'Courier New', monospace;
}

.games-left-label {
  font-size: 0.8rem;
  color: #6c757d;
  font-weight: 500;
}

.limit-warning {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: rgba(255, 193, 7, 0.2);
  border: 1px solid rgba(255, 193, 7, 0.4);
  border-radius: 0.25rem;
  color: #856404;
  font-weight: 600;
  font-size: 0.9rem;
}

.loading-text {
  color: #6c757d;
  font-style: italic;
  font-size: 0.9rem;
}

/* Welcome screen */
.welcome-container {
  width: 100%;
  max-width: 800px;
  margin: 2rem auto;
  padding: 2rem;
  text-align: center;
}

.welcome-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 2rem;
}

.welcome-nav {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

/* Welcome content styling */
.welcome-content {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 2px solid var(--secondary-color);
  border-radius: 1.5rem;
  padding: 2rem 1rem;
  box-shadow: 0 10px 25px rgba(52, 152, 219, 0.15);
  margin: 0 auto;
  text-align: center;
  width: min(100%, 800px);
  box-sizing: border-box;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.welcome-message h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: var(--primary-color);
  font-size: 2.2rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.welcome-message p {
  font-size: 1.1rem;
  color: #495057;
  margin: 1rem 0;
  line-height: 1.6;
}

.game-rules {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 1rem;
  padding: 1rem;
  margin: 1rem 0;
  text-align: left;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.game-rules h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--primary-color);
  text-align: center;
  font-size: 1.4rem;
}

.game-rules ul {
  list-style: none;
  padding: 0;
}

.game-rules li {
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(52, 152, 219, 0.1);
  font-size: 1rem;
  color: #495057;
}

.game-rules li:last-child {
  border-bottom: none;
}

.start-game-section {
  margin-top: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.start-new-game-button {
  background: linear-gradient(135deg, var(--success-color), #27ae60);
  color: white;
  border: none;
  border-radius: 1rem;
  padding: 1.5rem 3rem;
  font-size: 1.3rem;
  font-weight: 700;
  cursor: pointer;
  box-shadow: 0 5px 15px rgba(46, 204, 113, 0.3);
  display: flex;
  align-items: center;
  gap: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.start-new-game-button:hover {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  box-shadow: 0 8px 20px rgba(46, 204, 113, 0.4);
}


.start-game-icon {
  font-size: 1.5rem;
}

.start-game-text {
  font-family: inherit;
}

@media (max-width: 600px) {
  .welcome-message h2 {
    font-size: 1.8rem;
  }
  
  .start-new-game-button {
    padding: 1.2rem 2rem;
    font-size: 1.1rem;
  }
  
  .welcome-nav {
    gap: 0.5rem;
  }
  
  /* Welcome notification mobile styles */
  .welcome-notification {
    bottom: 10px;
    width: calc(100vw - 20px);
    max-width: calc(100vw - 20px);
    padding: 12px 16px;
    font-size: 14px;
    margin: 0 10px;
  }
  
  .welcome-notification .close-btn {
    font-size: 16px;
    margin-left: 8px;
  }
  
  /* Daily limit responsive styles */
  .daily-limit-container {
    padding: 1rem;
  }
  
  .limit-message {
    padding: 1rem;
  }
  
  .limit-details {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .limit-separator {
    order: -1;
    margin-bottom: 0.5rem;
  }
  
  .limit-actions {
    flex-direction: column;
    gap: 0.8rem;
  }
  
  .standings-link-button,
  .logout-link-button {
    width: 100%;
    max-width: 250px;
    margin: 0 auto;
  }

  /* Mobile content adjustments */
  .main-content {
    /* padding: 0.5rem; */
  }

  .welcome-content {
    padding: 1rem 0.5rem;
    min-height: auto;
  }

  .daily-limit-container {
    padding: 1rem;
  }

  .content-wrapper {
    padding: 0.5rem;
  }
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}


/* Welcome notification styles */
.welcome-notification {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-width: 90vw;
  width: 400px;
  font-weight: 500;
  text-align: center;
}

.welcome-notification .close-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  float: right;
  font-size: 18px;
  line-height: 1;
  margin-left: 12px;
  opacity: 0.8;
  padding: 0;
}

.welcome-notification .close-btn:hover {
  opacity: 1;
}



/* Win sequence styling */
.win-sequence {
  display: inline-block;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: #333;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 1.1em;
  margin-top: 10px;
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}



/* Achievement section styling */
.recent-win-info {
  margin-top: 20px;
  padding: 15px;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 10px;
  border-left: 4px solid #FFD700;
}

.recent-win-info h4 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 1.1em;
}

.achievement-badge {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: #333;
  padding: 12px 16px;
  border-radius: 8px;
  font-weight: 600;
  text-align: center;
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.2);
}

.leaderboard-note {
  margin-top: 15px;
  text-align: center;
  color: #6c757d;
  font-style: italic;
}

/* Authentication page styles */
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 1rem;
  box-sizing: border-box;
}

.auth-card {
  background: white;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  width: 100%;
  text-align: center;
}

.auth-title {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  font-size: 2rem;
  font-weight: 600;
}

.auth-subtitle {
  color: var(--text-color);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.username-form {
  margin-bottom: 1.5rem;
}

.username-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e0e0e0;
  border-radius: 0.5rem;
  font-size: 1rem;
  margin-bottom: 1rem;
  box-sizing: border-box;
}

.username-input:focus {
  outline: none;
  border-color: var(--secondary-color);
}

.auth-button {
  width: 100%;
  padding: 0.75rem 1.5rem;
  background: var(--secondary-color);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  margin-bottom: 0.5rem;
}

.auth-button:hover:not(:disabled) {
  background: #2980b9;
}

.auth-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.auth-button.secondary {
  background: var(--success-color);
}

.auth-button.secondary:hover:not(:disabled) {
  background: #27ae60;
}

.error-message {
  color: var(--danger-color);
  margin-top: 1rem;
  padding: 0.75rem;
  background: #ffebee;
  border-radius: 0.5rem;
  border-left: 4px solid var(--danger-color);
}

.webauthn-info {
  font-size: 0.9rem;
  color: #666;
  margin-top: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 0.5rem;
}

.loading {
  display: none;
}

.loading.active {
  display: inline-block;
}


/* Main content area */
.main-content {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  align-self: stretch;
  overflow-y: auto;
}

/* Content wrapper for swapped content */
.content-wrapper {
  width: 100%;
  flex: 1;
  padding: 1rem;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow-y: auto;
}

/* Fix for standings - align to top instead of center */
.content-wrapper.standings-wrapper {
  justify-content: flex-start;
  align-items: stretch;
}

/* Main game content area */
.main-game-content {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start; /* Start from top instead of space-between */
  min-height: 0; /* Allow shrinking */
  gap: 1rem; /* Add consistent spacing between elements */
}

/* Content header */
.content-header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(52, 152, 219, 0.2);
}

.content-header h2 {
  color: var(--primary-color);
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
}

/* Error messages */
.error {
  color: var(--danger-color);
  text-align: center;
  padding: 2rem;
  font-size: 1.1rem;
}


.player-standings-modal h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: var(--primary-color);
  text-align: center;
  font-size: 1.2rem;
}

.standings-loading {
  color: #6c757d;
  font-style: italic;
  font-size: 0.9rem;
  text-align: center;
  padding: 1rem;
}

.stats-loading {
  color: #6c757d;
  font-style: italic;
  font-size: 0.9rem;
  text-align: center;
  padding: 1rem;
}

.standings-error,
.stats-error {
  color: var(--danger-color);
  font-size: 0.9rem;
  text-align: center;
  padding: 1rem;
  background: rgba(231, 76, 60, 0.1);
  border-radius: 0.5rem;
  margin: 0.5rem;
}

.game-stats h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: var(--primary-color);
  text-align: center;
  font-size: 1.2rem;
}


.stats-row {
  display: flex;
  justify-content: space-around;
}

.stat-box {
  text-align: center;
  padding: 0.5rem;
  min-width: 60px;
}

.stat-value {
  font-size: 1.2rem;
  font-weight: bold;
  color: var(--primary-color);
}

.stat-label {
  font-size: 0.8rem;
  color: #7f8c8d;
}

/* Hint button */
.hint-container {
  display: flex;
  justify-content: center;
  margin: 0.25rem 0;
  flex-shrink: 0; /* Don't shrink */
}

.hint-button {
  background: #f1c40f;
  color: #34495e;
  border: none;
  border-radius: 2rem;
  padding: 0.4rem 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.hint-button:hover:not(:disabled) {
  background: #f39c12;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.hint-button:disabled {
  background: #ecf0f1;
  color: #95a5a6;
  cursor: not-allowed;
}

.hint-icon {
  font-size: 1.2rem;
}

select {
  padding: 0.5rem;
  border-radius: 0.25rem;
  border: 1px solid #ccc;
  font-family: inherit;
  background-color: white;
}

footer {
  flex: 0 0 auto; /* Don't grow or shrink */
  margin-top: auto; /* Push to bottom within flex container */
  padding: 0.75rem 0;
  color: #666;
  font-size: 0.9rem;
  text-align: center;
  width: 100%;
}

/* Layout System - Clean, predictable layout classes */
.page-container {
  width: min(100%, 800px);
  margin: 0 auto;
  box-sizing: border-box;
}

.content-area {
  width: 100%;
  padding: 1rem;
  box-sizing: border-box;
}

.centered-content {
  display: grid;
  place-items: center;
  width: 100%;
  box-sizing: border-box;
}


@media (max-width: 600px) {
  body {
    padding: 0.5rem;
    height: 100vh;
  }

  h1 {
    font-size: calc(var(--font-size-h1) * 1.2);
  }

  .game-container {
    padding: 0.8rem;
    height: calc(100vh - 1rem);
  }

  /* Adjust header for mobile - more compact */
  .game-header {
    /* grid-template-columns: 1fr; */
    grid-template-rows: auto auto auto;
    gap: 0.3rem;
    text-align: center;
    padding-bottom: 0.3rem;
    margin-bottom: 0.5rem;
  }

  .game-title {
    order: 0;
  }

  /* Mobile game layout adjustments for keyboard visibility */
  .game-main {
    max-height: 55vh; /* Slightly more space to prevent overlap */
    gap: 0.5rem;
    justify-content: space-between; /* Better distribution */
  }

  .hangman-display {
    max-height: 140px; /* Smaller on mobile */
    width: min(var(--svg-width), 200px);
    flex-shrink: 0;
    order: 0; /* Position first */
    margin-bottom: 0.5rem; /* Space before word display */
  }

  /* Countdown timer mobile styles - now above hangman */
  .countdown-timer {
    margin: 0.25rem 0;
    padding: 0.25rem 0;
    order: -1; /* Position before gallows */
  }
  
  .countdown-number {
    font-size: 1.5rem; /* Smaller on mobile */
  }

  .word-display {
    margin: 0.75rem 0; /* More space from gallows */
    font-size: clamp(1rem, 4vw, 1.5rem); /* Smaller on mobile */
    order: 1; /* Position after gallows */
  }

  .status {
    margin: 0.25rem 0;
    font-size: 1rem;
    order: -2; /* Position before timer and gallows */
  }

  .hint-container {
    margin: 0.5rem 0; /* More space to prevent overlap */
    order: 2; /* Position after word display */
  }

  .hint-button {
    padding: 0.3rem 0.8rem;
    font-size: 0.9rem;
  }

  /* Ensure keyboard has adequate space */
  .keyboard-container {
    min-height: 120px; /* Minimum space for mobile keyboard */
  }

  .keyboard {
    margin-top: 0.25rem;
  }

  .game-nav {
    order: 3;
    justify-self: center;
    gap: 1rem;
  }

  .game-title h2 {
    margin-bottom: 0;
    font-size: clamp(1rem, 3vw, 1.2rem);
  }

  /* Make difficulty pills more compact on mobile */
  .difficulty-pill {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    min-width: 35px;
  }

  /* Adjust category dropdown for mobile */
  .category-dropdown select {
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
    padding-right: 1.8rem;
    background-size: 1rem;
  }

  /* Make navigation buttons smaller and more compact on mobile */
  .dashboard-toggle,
  .standings-button,
  .new-game-nav,
  .logout-button {
    width: 1.8rem;
    height: 1.8rem;
  }

  .dashboard-icon,
  .new-game-icon,
  .logout-icon {
    font-size: 0.9rem;
  }

  footer {
    margin-bottom: clamp(1rem, 3vw, 2rem);
  }

  .letter {
    border-bottom-width: 3px;
  }

  .difficulty-selector {
    margin-top: clamp(1rem, 3vw, 1.5rem);
  }

  /* Add extra touch area for better mobile experience */
  button {
    position: relative;
  }

  button::after {
    content: '';
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
    z-index: -1;
  }
}

/* Extra small screens - ensure keyboard is always visible */
@media (max-height: 600px) {
  .game-main {
    max-height: 45vh; /* Very restricted on tiny screens */
    justify-content: space-between;
  }

  .hangman-display {
    max-height: 100px;
    width: min(var(--svg-width), 160px);
    margin-bottom: 0.5rem;
  }

  .countdown-number {
    font-size: 1.2rem;
  }

  .word-display {
    font-size: clamp(0.9rem, 4vw, 1.2rem);
    margin: 0.5rem 0;
  }

  .hint-container {
    margin: 0.25rem 0;
  }

  .keyboard-container {
    min-height: 100px;
  }
}

/* Daily limit status styling */
.daily-limit-status {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e8f5e8;
  border: 1px solid #c3e6c3;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  margin: 1rem 0;
  font-size: 0.9rem;
  font-weight: 500;
}

.daily-limit-status.near-limit {
  background: #fff3cd;
  border-color: #ffeaa7;
  color: #856404;
}

.daily-limit-status.limit-reached {
  background: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.daily-limit-status .limit-icon {
  margin-right: 0.5rem;
  font-size: 1rem;
}

.daily-limit-status .limit-text {
  flex: 1;
}

