/**
 * Client-side game logic extracted from inline templates
 * Handles notifications, navigation, timers, and HTMX interactions
 */

// Initialize game client when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  initializeGameClient();
});

function initializeGameClient() {
  // Initialize welcome notification check
  initializeWelcomeNotification();
  
  // Initialize game timer
  initializeGameTimer();
  
  // Setup HTMX event listeners
  setupHtmxEventListeners();
  
  // Setup navigation management
  setupNavigationManagement();
}

// Welcome notification management
function initializeWelcomeNotification() {
  const urlParams = new URLSearchParams(window.location.search);
  const isWelcome = urlParams.get('welcome') === 'true';
  
  if (isWelcome) {
    showWelcomeNotification();
    // Clean up URL without reloading page
    const url = new URL(window.location);
    url.searchParams.delete('welcome');
    window.history.replaceState({}, document.title, url.pathname + url.search);
  }
}

function showWelcomeNotification() {
  const notification = document.createElement('div');
  notification.className = 'welcome-notification';
  notification.innerHTML = `
    <button class="close-btn" onclick="closeWelcomeNotification(this.parentElement)">&times;</button>
    <div>
      <strong>🎉 Welcome to Hangman!</strong><br>
      Your account has been created successfully. Enjoy the game!
    </div>
  `;
  
  document.body.appendChild(notification);
  
  // Auto-hide after 5 seconds
  setTimeout(() => {
    if (notification.parentElement) {
      closeWelcomeNotification(notification);
    }
  }, 5000);
}

function closeWelcomeNotification(notification) {
  notification.classList.add('fade-out');
  setTimeout(() => {
    if (notification.parentElement) {
      notification.parentElement.removeChild(notification);
    }
  }, 300);
}

// Make closeWelcomeNotification global for onclick handler
window.closeWelcomeNotification = closeWelcomeNotification;

// Game timer management
if (typeof window.gameTimerState === 'undefined') {
  window.gameTimerState = {
    startTime: null,
    timeLimit: null,
    interval: null,
    gameId: null,
    timeExpiredSent: false
  };
}
const gameTimerState = window.gameTimerState;

function initializeGameTimer() {
  initializeOrUpdateTimer();
}

function initializeOrUpdateTimer() {
  const timerElement = document.getElementById('countdown-timer');
  const numberElement = document.getElementById('countdown-number');
  
  if (!timerElement || !numberElement) {
    // No timer element means game is not playing, clear any existing timer
    if (gameTimerState.interval) {
      clearInterval(gameTimerState.interval);
      gameTimerState.interval = null;
    }
    return;
  }
  
  const startTime = parseInt(timerElement.dataset.startTime);
  const timeLimit = parseInt(timerElement.dataset.timeLimit);
  const gameId = timerElement.dataset.gameId || 'unknown';
  
  if (!startTime || !timeLimit) return;
  
  // Check if this is a new game or same game
  const isNewGame = gameTimerState.gameId !== gameId || 
                    gameTimerState.startTime !== startTime;
  
  if (isNewGame) {
    // Clear old timer if exists
    if (gameTimerState.interval) {
      clearInterval(gameTimerState.interval);
    }
    
    // Update timer state for new game
    gameTimerState.startTime = startTime;
    gameTimerState.timeLimit = timeLimit;
    gameTimerState.gameId = gameId;
    gameTimerState.timeExpiredSent = false; // Reset flag for new game
    
    // Start new timer
    startNewTimer();
  } else {
    // Same game, just update the display without restarting timer
    updateTimerDisplay();
  }
}

function startNewTimer() {
  function updateTimer() {
    const elapsedTime = (Date.now() - gameTimerState.startTime) / 1000;
    const remainingTime = Math.max(0, Math.ceil(gameTimerState.timeLimit - elapsedTime));
    
    // Try to update display (defensive - don't stop timer if elements temporarily missing)
    updateTimerDisplay(remainingTime);
    
    // Auto-submit when time runs out
    if (remainingTime <= 0) {
      clearInterval(gameTimerState.interval);
      gameTimerState.interval = null;
      
      // Add flag to prevent multiple time-expired requests
      if (gameTimerState.timeExpiredSent) {
        return;
      }
      gameTimerState.timeExpiredSent = true;
      
      // Submit time expired request using HTMX with better error handling
      try {
        htmx.ajax('POST', '/game/time-expired', {
          target: '#main-content',
          swap: 'innerHTML'
        }).catch(error => {
          // Reset flag if request failed so it can be retried
          gameTimerState.timeExpiredSent = false;
          window.gameLogger?.error('CLIENT', 'Error handling time expired:', error) || 
          console.error('Error handling time expired:', error);
        });
      } catch (error) {
        // Reset flag if HTMX not available
        gameTimerState.timeExpiredSent = false;
        console.error('HTMX not available for time expired request:', error);
      }
      
      return;
    }
  }
  
  // Reset time expired flag for new timer
  gameTimerState.timeExpiredSent = false;
  
  // Update immediately
  updateTimer();
  
  // Update every second
  gameTimerState.interval = setInterval(updateTimer, 1000);
}

function updateTimerDisplay(remainingTime) {
  const numberElement = document.getElementById('countdown-number');
  if (!numberElement) return;
  
  // Calculate remaining time if not provided
  if (remainingTime === undefined) {
    const elapsedTime = (Date.now() - gameTimerState.startTime) / 1000;
    remainingTime = Math.max(0, Math.ceil(gameTimerState.timeLimit - elapsedTime));
  }
  
  numberElement.textContent = remainingTime.toString();
  
  // Update color classes based on remaining time
  numberElement.className = 'countdown-number';
  if (remainingTime >= 21) {
    numberElement.classList.add('time-normal');
  } else if (remainingTime >= 11) {
    numberElement.classList.add('time-warning');
  } else {
    numberElement.classList.add('time-critical');
  }
  
  // Update hangman face expression based on time remaining
  updateHangmanFaceExpression(remainingTime);
}

// Update hangman face expression based on timer
function updateHangmanFaceExpression(remainingTime) {
  const hangmanHead = document.querySelector('.hangman-head');
  if (!hangmanHead) return; // No head visible yet (< 2 wrong guesses)
  
  let newExpression = '';
  
  // Determine which face expression should be shown
  if (remainingTime > 20) {
    newExpression = 'happy';
  } else if (remainingTime > 10) {
    newExpression = 'worrying'; 
  } else {
    newExpression = 'scared';
  }
  
  // Check if we need to update the expression
  const currentExpression = hangmanHead.dataset.expression || 'happy';
  if (currentExpression === newExpression) {
    return; // No change needed
  }
  
  // Update the face expression
  hangmanHead.dataset.expression = newExpression;
  updateHangmanFaceSvg(hangmanHead, newExpression);
}

// Update the SVG content for hangman face
function updateHangmanFaceSvg(headElement, expression) {
  let faceContent = '';
  
  switch (expression) {
    case 'happy':
      faceContent = `
        <circle cx="100" cy="70" r="15" fill="white" stroke="var(--primary-color)" stroke-width="4" />
        <circle cx="93" cy="65" r="2" fill="black" />
        <circle cx="107" cy="65" r="2" fill="black" />
        <path d="M92 75c0 0 5 5 16 0" stroke="black" stroke-width="2" stroke-linecap="round" fill="none" />
      `;
      break;
    case 'worrying':
      faceContent = `
        <circle cx="100" cy="70" r="15" fill="white" stroke="var(--primary-color)" stroke-width="4" />
        <circle cx="93" cy="65" r="2" fill="black" />
        <circle cx="107" cy="65" r="2" fill="black" />
        <path d="M92 75h16" stroke="orange" stroke-width="2" stroke-linecap="round" fill="none" />
        <path d="M96 60v2M104 60v2" stroke="orange" stroke-width="1.5" stroke-linecap="round" fill="none" />
      `;
      break;
    case 'scared':
      faceContent = `
        <circle cx="100" cy="70" r="15" fill="white" stroke="var(--primary-color)" stroke-width="4" />
        <circle cx="93" cy="65" r="3" fill="black" />
        <circle cx="107" cy="65" r="3" fill="black" />
        <ellipse cx="100" cy="77" rx="3" ry="5" fill="red" stroke="red" stroke-width="1" />
        <path d="M96 60v3M104 60v3" stroke="red" stroke-width="2" stroke-linecap="round" fill="none" />
      `;
      break;
    case 'sad':
      faceContent = `
        <circle cx="100" cy="70" r="15" fill="white" stroke="var(--primary-color)" stroke-width="4" />
        <circle cx="93" cy="65" r="2" fill="black" />
        <circle cx="107" cy="65" r="2" fill="black" />
        <path d="M92 80c0 0 5 -5 16 0" stroke="blue" stroke-width="2" stroke-linecap="round" fill="none" />
        <path d="M95 63l2 2M103 63l2 2" stroke="blue" stroke-width="1" stroke-linecap="round" fill="none" />
      `;
      break;
    default:
      return;
  }
  
  // Update the SVG content
  headElement.innerHTML = faceContent;
}

// Navigation management
function setupNavigationManagement() {
  // Make setActiveNav global for onclick handlers
  window.setActiveNav = setActiveNav;
}

function setActiveNav(clickedButton) {
  // Remove active class from all nav buttons
  document.querySelectorAll('.game-nav .dashboard-toggle, .game-nav .standings-button, .game-nav .new-game-nav').forEach(btn => {
    btn.classList.remove('nav-btn-active');
  });
  
  // Add active class to clicked button
  clickedButton.classList.add('nav-btn-active');
  
  // Check if this is a new game button and if there's an active game
  const isNewGameButton = clickedButton.classList.contains('new-game-nav');
  const hasActiveGame = document.querySelector('.keyboard-container');
  
  if (isNewGameButton && hasActiveGame) {
    // Check if the game is in "playing" status
    const gameStatus = getGameStatus();
    
    if (gameStatus === 'playing') {
      // Disable ALL nav buttons during active gameplay to prevent cheating
      document.querySelectorAll('.game-nav .dashboard-toggle, .game-nav .standings-button, .game-nav .new-game-nav').forEach(btn => {
        btn.classList.add('nav-btn-disabled');
      });
    } else {
      // Game is won/lost, enable all buttons
      enableAllNavButtons();
    }
  } else {
    // Not new game or no active game, enable all buttons
    enableAllNavButtons();
  }
  
  clickedButton.blur();
}

function enableAllNavButtons() {
  document.querySelectorAll('.game-nav .dashboard-toggle, .game-nav .standings-button, .game-nav .new-game-nav').forEach(btn => {
    btn.classList.remove('nav-btn-disabled');
  });
}

function getGameStatus() {
  // Look for status indicators in the game content
  const statusElement = document.querySelector('.status-display');
  const countdownTimer = document.querySelector('#countdown-timer');
  const keyboardContainer = document.querySelector('.keyboard-container');
  
  
  if (statusElement) {
    if (statusElement.textContent?.includes('won') || statusElement.textContent?.includes('🎉')) {
      return 'won';
    }
    if (statusElement.textContent?.includes('Game Over') || statusElement.textContent?.includes('💀')) {
      return 'lost';
    }
  }
  
  // Check if countdown timer is active (indicates playing)
  if (countdownTimer) {
    return 'playing';
  }
  
  // Alternative check: if keyboard exists, game is likely playing
  if (keyboardContainer) {
    return 'playing';
  }
  
  return 'not-playing';
}

// HTMX event listeners
function setupHtmxEventListeners() {
  // Listen for HTMX events to reinitialize timer after updates
  document.addEventListener('htmx:afterSettle', function(event) {
    if (event.detail.target && event.detail.target.id === 'main-content') {
      // Only reinitialize timer, don't clear it aggressively
      initializeOrUpdateTimer();
      // Update nav button states based on new content
      updateNavButtonStates();
    }
  });
}

function updateNavButtonStates() {
  // Find which nav button should be active based on current content
  const hasGameContent = document.querySelector('.keyboard-container');
  const hasStatsContent = document.querySelector('.game-stats');
  const hasStandingsContent = document.querySelector('.player-standings');
  
  // Check if there's an active game in progress
  const gameStatus = getGameStatus();
  const isActiveGame = hasGameContent && gameStatus === 'playing';
  
  
  // Remove active class from all nav buttons first
  document.querySelectorAll('.game-nav .dashboard-toggle, .game-nav .standings-button, .game-nav .new-game-nav').forEach(btn => {
    btn.classList.remove('nav-btn-active');
  });
  
  // Determine which button should be active and set it
  if (hasGameContent) {
    document.querySelector('.new-game-nav')?.classList.add('nav-btn-active');
  } else if (hasStatsContent) {
    document.querySelector('.dashboard-toggle')?.classList.add('nav-btn-active');
  } else if (hasStandingsContent) {
    document.querySelector('.standings-button')?.classList.add('nav-btn-active');
  }
  
  // Disable/enable nav buttons based on game state
  if (isActiveGame) {
    // Disable ALL nav buttons during active gameplay to prevent cheating
    document.querySelectorAll('.game-nav .dashboard-toggle, .game-nav .standings-button, .game-nav .new-game-nav').forEach(btn => {
      btn.classList.add('nav-btn-disabled');
    });
  } else {
    // Enable all buttons when no active game
    enableAllNavButtons();
  }
}

// HTMX error handlers  
document.addEventListener('htmx:responseError', function(event) {
  window.gameLogger?.error('CLIENT', 'HTMX response error:', event.detail) || 
  console.error('HTMX response error:', event.detail);
});

// Handle HTMX network errors
document.addEventListener('htmx:sendError', function(event) {
  window.gameLogger?.error('CLIENT', 'HTMX send error:', event.detail) || 
  console.error('HTMX send error:', event.detail);
});


// Utility functions
function logError(context, message, error) {
  if (window.gameLogger) {
    window.gameLogger.error(context, message, error);
  } else {
    console.error(`[${context}] ${message}`, error);
  }
}

function logInfo(context, message, data) {
  if (window.gameLogger) {
    window.gameLogger.log(context, message, data);
  } else {
    console.log(`[${context}] ${message}`, data);
  }
}

// Export functions for testing or external use
window.gameClient = {
  initializeGameClient,
  showWelcomeNotification,
  closeWelcomeNotification,
  initializeOrUpdateTimer,
  setActiveNav,
  gameTimerState
};