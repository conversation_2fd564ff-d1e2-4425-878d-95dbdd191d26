import { GameState } from "../types.ts";
import { match } from "../../../libs/utils/pattern.ts";
import { 
  createBaseLayout, 
  createGameHeader, 
  createStatusDisplay, 
  createCountdownTimer, 
  generateHangmanSvg, 
  generateTimerAwareHangmanSvg,
  createCelebrationSvg, 
  createWordDisplay, 
  createHintButton, 
  createKeyboard 
} from "./components.ts";

export const homePage = (content: string): string => 
  createBaseLayout("Secure Hangman Game", content);

// Full game component with container and nav (for full page loads)
export const gameComponent = (state: GameState, gamesRemaining?: number): string => `
<div class="game-container" id="game-container">
  ${createGameHeader(state.username)}
  
  <!-- Main game content area that can be swapped -->
  <div id="main-game-content" class="main-game-content">
    ${gameContentOnly(state, gamesRemaining)}
  </div>
</div>
`;

// Just the game content for HTMX swapping (no container, no nav)
export const gameContentOnly = (state: GameState, gamesRemaining?: number): string => `
  ${createStatusDisplay(state, gamesRemaining)}

  <!-- Main game area -->
  <div class="game-main">
    ${createCountdownTimer(state)}
    ${hangmanSvg(state)}
    ${createWordDisplay(state)}
    ${createHintButton(state)}
  </div>

  ${createKeyboard(state)}
`;

// SVG Components for hangman visualization with timer-aware face expressions
export const hangmanSvg = (state: GameState): string => {
  const { status, wrongGuesses } = state;

  return match(status)
    .with("won", () => createCelebrationSvg())
    .with("lost", () => generateHangmanSvg(wrongGuesses, true, 'sad'))
    .with("playing", () => {
      // Calculate time remaining for timer-aware face expression
      const currentTime = Date.now();
      const elapsedTime = (currentTime - state.startTime) / 1000; // in seconds
      const timeRemaining = Math.max(0, state.timeLimit - elapsedTime);
      
      return generateTimerAwareHangmanSvg(wrongGuesses, false, timeRemaining);
    })
    .exhaustive();
};

// Note: Legacy function exports removed - use create* functions from components.ts instead

// Note: difficultySelector function removed - not used anywhere in codebase

/**
 * Display game statistics content
 */
export const gameStatsContent = (state: GameState): string => {
  const { statistics } = state;
  const gameTime = state.endTime ? Math.round((state.endTime - state.startTime) / 1000) : 0;

  return `
    <div class="stats-row">
      <div class="stat-box">
        <div class="stat-value">${statistics.gamesPlayed}</div>
        <div class="stat-label">Games</div>
      </div>
      <div class="stat-box">
        <div class="stat-value">${statistics.gamesWon}</div>
        <div class="stat-label">Wins</div>
      </div>
      <div class="stat-box">
        <div class="stat-value">${Math.round((statistics.gamesWon / (statistics.gamesPlayed || 1)) * 100)}%</div>
        <div class="stat-label">Win Rate</div>
      </div>
    </div>
    <div class="stats-row">
      <div class="stat-box">
        <div class="stat-value">${statistics.currentStreak}</div>
        <div class="stat-label">Streak</div>
      </div>
      <div class="stat-box">
        <div class="stat-value">${statistics.bestStreak}</div>
        <div class="stat-label">Best</div>
      </div>
      <div class="stat-box">
        <div class="stat-value">${state.status !== "playing" ? gameTime : ""}</div>
        <div class="stat-label">${state.status !== "playing" ? "Seconds" : ""}</div>
      </div>
    </div>
    
    
    ${state.winSequenceNumber ? `
    <div class="recent-win-info">
      <h4>🏆 Recent Achievement</h4>
      <div class="achievement-badge">
        You are the <strong>#${state.winSequenceNumber}</strong> person to successfully complete this challenge!
      </div>
    </div>
    ` : ''}
    
    <div class="leaderboard-note">
      <small>📊 Global win tracking helps celebrate everyone's success!</small>
    </div>
  `;
};

// Note: gameStats function removed - use gameStatsContent directly or create wrapper in components.ts if needed

// Note: Legacy function exports removed - use create* functions from components.ts instead

/**
 * Player standings modal content
 */
export const playerStandingsContent = (standings: unknown[], currentUser?: string): string => {
  if (!standings || standings.length === 0) {
    return `
      <div class="standings-empty">
        <p>No players have won games yet. Be the first!</p>
      </div>
    `;
  }

  return `
    <div class="standings-header">
      <span class="rank-header">Rank</span>
      <span class="player-header">Player</span>
      <span class="wins-header">Wins</span>
      <span class="time-header">Avg Time</span>
    </div>
    <div class="standings-list">
      ${standings.map((standing, index) => {
        const s = standing as any; // Type assertion for standings data
        return `
        <div class="standing-row ${s.username === currentUser ? 'current-user' : ''}">
          <span class="rank">${index + 1}</span>
          <span class="player-name">${s.displayName}</span>
          <span class="wins">${s.totalWins}</span>
          <span class="avg-time">${s.averageTime}s</span>
        </div>
      `;}).join('')}
    </div>
    <div class="standings-note">
      <small>Ranked by total wins, then by average completion time</small>
    </div>
  `;
};


/**
 * Daily limit reached page
 */
export const dailyLimitReachedPage = (gamesPlayed: number, _gamesRemaining: number, username?: string): string => {
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  tomorrow.setHours(0, 0, 0, 0);
  const _resetTime = tomorrow.toLocaleString();

  return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Daily Limit Reached - Hangman Game</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="/static/styles.css">
</head>
<body>
  <div class="daily-limit-container">
    <div class="limit-message">
      <h2>🎯 Daily Game Limit Reached</h2>
      <div class="limit-details">
        <div class="games-played">
          <span class="limit-number">${gamesPlayed}</span>
          <span class="limit-label">Games Played Today</span>
        </div>
        <div class="limit-separator">of</div>
        <div class="games-total">
          <span class="limit-number">5</span>
          <span class="limit-label">Daily Maximum</span>
        </div>
      </div>
      
      <div class="reset-info">
        <p>🕛 Your 5 games are up.</p>
        <p class="reset-time">See how you compare to other players!</p>
      </div>

      <div class="limit-actions">
        <!-- Statistics and Standings buttons -->
        ${username ? `
          <button class="standings-link-button" 
                  hx-get="/api/user-stats" 
                  hx-target="#main-content" 
                  hx-swap="innerHTML">
            📊 View Statistics
          </button>
          
          <button class="standings-link-button" 
                  hx-get="/api/standings" 
                  hx-target="#main-content" 
                  hx-swap="innerHTML">
            🏆 View Standings
          </button>
        ` : ''}
        
        ${username ? `
          <button class="logout-link-button" hx-post="/auth/logout">
            👋 Logout
          </button>
        ` : ''}
      </div>
      
      <div class="limit-note">
        <small>Daily limits help ensure fair gameplay and encourage balanced gaming habits.</small>
      </div>
    </div>
  </div>

  
  <footer>
    <p>Cooked with ❤️ by <a href="https://srdjan.github.io" target="_blank" rel="noopener noreferrer">⊣˚∆˚⊢</a></p>
  </footer>

  <script>
    // Stats and standings are loaded via HTMX navigation buttons
  </script>
</body>
</html>
  `;
};

/**
 * Daily limit reached content (for HTMX content swapping only)
 */
export const dailyLimitReached = (gamesPlayed: number, _gamesRemaining: number, username?: string): string => {
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  tomorrow.setHours(0, 0, 0, 0);
  const _resetTime = tomorrow.toLocaleString();

  return `
  <!-- Daily limit content for HTMX swapping (no nav bar, no main-content wrapper) -->
  <div class="daily-limit-container">
    <div class="limit-message">
      <h2>🎯 Daily Game Limit Reached</h2>
      <div class="limit-details">
        <div class="games-played">
          <span class="limit-number">${gamesPlayed}</span>
          <span class="limit-label">Games Played Today</span>
        </div>
        <div class="limit-separator">of</div>
        <div class="games-total">
          <span class="limit-number">5</span>
          <span class="limit-label">Daily Maximum</span>
        </div>
      </div>
      
      <div class="reset-info">
        <p>🕛 Your 5 games are up!</p>
        <p class="reset-time">Watch the standings to see where you rank!</p>
      </div>

      <div class="limit-actions">
        ${username ? `
          <button class="logout-link-button" hx-post="/auth/logout">
            👋 Logout
          </button>
        ` : ''}
      </div>
      
      <div class="limit-note">
        <small>Daily limits help ensure fair gameplay and encourage balanced gaming habits.</small>
      </div>
    </div>
  </div>
  `;
};
/**
 * Games remaining indicator for active games
 */
export const gamesRemainingIndicator = async (username: string): Promise<string> => {
  try {
    const { checkDailyLimit } = await import("../../../libs/auth/kv.ts");
    const limitCheck = await checkDailyLimit(username);
    
    return `
    <div class="games-remaining">
      <span class="remaining-count">${limitCheck.gamesRemaining}</span>
      <span class="remaining-label">games remaining today</span>
    </div>
    `;
  } catch (error) {
    console.error("Error getting games remaining:", error);
    return '';
  }
};

/**
 * Welcome screen component for users with no active game
 */
export const welcomeScreen = (username?: string): string => {
  return `
  <div class="game-container" id="game-container">
    <!-- Top navigation bar -->
    <div class="game-header">
      <div class="game-title">
        <h2>Hangman</h2>
        ${username ? `<div class="user-info">Hi, ${username.split('@')[0]}!</div>` : ''}
      </div>

      <!-- Empty space where countdown would be -->
      <div class="countdown-timer" style="visibility: hidden;"></div>

      <div class="game-nav">
        <!-- Dashboard toggle button -->
        <button
          class="dashboard-toggle"
          aria-label="View statistics dashboard"
          hx-get="/api/user-stats"
          hx-target="#main-content"
          hx-swap="innerHTML"
          onclick="setActiveNav(this)"
        >
          <span class="dashboard-icon">📊</span>
        </button>
        
        <!-- Standings button -->
        <button
          class="standings-button"
          aria-label="View player standings"
          hx-get="/api/standings"
          hx-target="#main-content"
          hx-swap="innerHTML"
          onclick="setActiveNav(this)"
        >
          <span class="standings-icon">🏆</span>
        </button>
        
        <!-- New Game button - active on welcome screen -->
        <button
          class="new-game-nav"
          aria-label="Start New Game"
          hx-post="/new-game"
          hx-target="#main-content"
          hx-swap="innerHTML"
        >
          <span class="new-game-icon">🎮</span>
        </button>
        
        ${username ? `
          <!-- Logout button -->
          <button
            class="logout-button"
            aria-label="Logout"
            hx-post="/auth/logout"
          >
            <span class="logout-icon">👋</span>
          </button>
        ` : ''}
      </div>
    </div>

    <!-- Main content area that will be swapped -->
    <div id="main-content" class="main-content">
      <!-- Welcome content -->
      <div class="welcome-content">
      <div class="welcome-message">
        <h3>🎯 Ready to Play Hangman?</h3>
        
        <div class="game-rules">
          <h3>📋 Game Rules</h3>
          <ul>
            <li>🎮 Click the <span class="new-game-icon">🎮</span> to begin the game</li>
            <li>🎲 Guess the word letter by letter</li>
            <li>⏰ <strong>60 secs</strong> to complete each game</li>
            <li>💡 You get <strong>1 hint</strong> per game</li>
            <li>🎮 Play <strong>5 games</strong> and see where are you ranked</li>
            <li>🏆 Compete for the best completion times on the leaderboard</li>
          </ul>
        </div>
        
      </div>
    </div>
  </div>
  </div>
  
  <script>
    // Simple nav menu management
    function setActiveNav(clickedButton) {
      // Remove active class from all nav buttons
      document.querySelectorAll('.dashboard-toggle, .standings-button, .new-game-nav').forEach(btn => {
        btn.classList.remove('nav-btn-active');
      });
      
      // Add active class to clicked button
      clickedButton.classList.add('nav-btn-active');
      
      // Enable all nav buttons by default
      document.querySelectorAll('.dashboard-toggle, .standings-button, .new-game-nav').forEach(btn => {
        btn.classList.remove('nav-btn-disabled');
      });
      
      clickedButton.blur();
    }
  </script>
  `;
};