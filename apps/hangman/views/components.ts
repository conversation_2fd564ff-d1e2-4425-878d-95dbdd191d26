import { GameState } from "../types.ts";
import { match } from "../../../libs/utils/pattern.ts";

/**
 * Reusable template components for the hangman game
 * Consolidates repetitive HTML patterns and reduces duplication
 */

// Base HTML layout template
export const createBaseLayout = (
  title: string,
  content: string,
  scripts: string[] = [],
  styles: string[] = []
): string => `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${title}</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">
  <script src="https://unpkg.com/htmx.org@2.0.4"></script>
  <link rel="stylesheet" href="/static/styles.css">
  ${styles.map(style => `<link rel="stylesheet" href="${style}">`).join('\n  ')}
  
  <!-- Accessibility features -->
  <div id="screen-reader-announcer" aria-live="assertive" aria-atomic="true" class="sr-only"></div>
</head>
<body>
  <header></header>
  
  ${content}
  
  <footer>
    <p>Cooked with ❤️ by <a href="https://srdjan.github.io" target="_blank" rel="noopener noreferrer">⊣˚∆˚⊢</a></p>
  </footer>
  
  <script src="/static/keyboard.js" defer></script>
  <script src="/static/game-client.js" defer></script>
  ${scripts.map(script => `<script src="${script}" defer></script>`).join('\n  ')}
</body>
</html>
`;

// Navigation button component
export const createNavButton = (
  className: string,
  label: string,
  icon: string,
  attributes: Record<string, string> = {},
  disabled = false
): string => {
  const attrString = Object.entries(attributes)
    .map(([key, value]) => `${key}="${value}"`)
    .join(' ');
  
  return `
    <button
      class="${className}${disabled ? ' disabled' : ''}"
      aria-label="${label}"
      ${disabled ? 'disabled' : ''}
      ${attrString}
      onclick="setActiveNav(this)"
    >
      <span class="${className.replace('-button', '-icon').replace('-toggle', '-icon')}">${icon}</span>
    </button>
  `;
};

// Game header component
export const createGameHeader = (username?: string): string => `
  <div class="game-header">
    <div class="game-title">
      <h2>Hangman</h2>
      ${username ? `<div class="user-info">Hi, ${username.split('@')[0]}!</div>` : ''}
    </div>
    <div></div>
    <div class="game-nav">
      ${createNavButton(
        'dashboard-toggle',
        'View statistics dashboard',
        '📊',
        { 'hx-get': '/api/user-stats', 'hx-target': '#main-content', 'hx-swap': 'innerHTML' }
      )}
      ${createNavButton(
        'standings-button',
        'View player standings',
        '🏆',
        { 'hx-get': '/api/standings', 'hx-target': '#main-content', 'hx-swap': 'innerHTML' }
      )}
      ${createNavButton(
        'new-game-nav',
        'New Game',
        '🎮',
        { 'hx-post': '/new-game', 'hx-target': '#main-content', 'hx-swap': 'innerHTML' }
      )}
      ${username ? `
        <button
          class="logout-button"
          aria-label="Logout"
          hx-post="/auth/logout"
        >
          <span class="logout-icon">👋</span>
        </button>
      ` : ''}
    </div>
  </div>
`;

// SVG component factory
export const createSvgComponent = (
  className: string,
  viewBox: string,
  content: string,
  attributes: Record<string, string> = {}
): string => {
  const attrString = Object.entries(attributes)
    .map(([key, value]) => `${key}="${value}"`)
    .join(' ');
  
  return `
    <div class="${className}" ${attrString}>
      <svg class="${className.replace('-display', '-figure')}" viewBox="${viewBox}" aria-hidden="true" fill="none">
        ${content}
      </svg>
    </div>
  `;
};

// Hangman SVG parts
export const HANGMAN_PARTS = {
  gallows: `
    <path d="M20 180h160M60 180l-20-140h120l-20 140" stroke="var(--primary-color)" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" fill="none" />
    <path d="M30 40h140" stroke="var(--primary-color)" stroke-width="4" stroke-linecap="round" fill="none" />
  `,
  rope: `<path class="hangman-part visible" d="M100 40v30" stroke="var(--primary-color)" stroke-width="4" stroke-linecap="round" fill="none" />`,
  // Sad face expression (game lost/time expired)
  headSad: `
    <g class="hangman-part hangman-head visible" data-expression="sad" fill="none">
      <circle cx="100" cy="70" r="15" fill="white" stroke="var(--primary-color)" stroke-width="4" />
      <circle cx="93" cy="65" r="2" fill="black" />
      <circle cx="107" cy="65" r="2" fill="black" />
      <path d="M92 80c0 0 5 -5 16 0" stroke="blue" stroke-width="2" stroke-linecap="round" fill="none" />
      <path d="M95 63l2 2M103 63l2 2" stroke="blue" stroke-width="1" stroke-linecap="round" fill="none" />
    </g>
  `,
  
  // Happy face expression (default/starting)
  headHappy: `
    <g class="hangman-part hangman-head visible" data-expression="happy" fill="none">
      <circle cx="100" cy="70" r="15" fill="white" stroke="var(--primary-color)" stroke-width="4" />
      <circle cx="93" cy="65" r="2" fill="black" />
      <circle cx="107" cy="65" r="2" fill="black" />
      <path d="M92 75c0 0 5 5 16 0" stroke="black" stroke-width="2" stroke-linecap="round" fill="none" />
    </g>
  `,
  
  // Worrying face expression (20 seconds left)
  headWorrying: `
    <g class="hangman-part hangman-head visible" data-expression="worrying" fill="none">
      <circle cx="100" cy="70" r="15" fill="white" stroke="var(--primary-color)" stroke-width="4" />
      <circle cx="93" cy="65" r="2" fill="black" />
      <circle cx="107" cy="65" r="2" fill="black" />
      <path d="M92 75h16" stroke="orange" stroke-width="2" stroke-linecap="round" fill="none" />
      <path d="M96 60v2M104 60v2" stroke="orange" stroke-width="1.5" stroke-linecap="round" fill="none" />
    </g>
  `,
  
  // Scared face expression (10 seconds left)
  headScared: `
    <g class="hangman-part hangman-head visible" data-expression="scared" fill="none">
      <circle cx="100" cy="70" r="15" fill="white" stroke="var(--primary-color)" stroke-width="4" />
      <circle cx="93" cy="65" r="3" fill="black" />
      <circle cx="107" cy="65" r="3" fill="black" />
      <ellipse cx="100" cy="77" rx="3" ry="5" fill="red" stroke="red" stroke-width="1" />
      <path d="M96 60v3M104 60v3" stroke="red" stroke-width="2" stroke-linecap="round" fill="none" />
    </g>
  `,
  body: `<path class="hangman-part visible" d="M100 85v50" stroke="var(--primary-color)" stroke-width="4" stroke-linecap="round" fill="none" />`,
  leftArm: `<path class="hangman-part visible" d="M100 95c-10 5-20 15-30 20" stroke="var(--primary-color)" stroke-width="4" stroke-linecap="round" fill="none" />`,
  rightArm: `<path class="hangman-part visible" d="M100 95c10 5 20 15 30 20" stroke="var(--primary-color)" stroke-width="4" stroke-linecap="round" fill="none" />`,
  leftLeg: `<path class="hangman-part visible" d="M100 135c-8 8-17 17-25 25" stroke="var(--primary-color)" stroke-width="4" stroke-linecap="round" fill="none" />`,
  rightLeg: `<path class="hangman-part visible" d="M100 135c8 8 17 17 25 25" stroke="var(--primary-color)" stroke-width="4" stroke-linecap="round" fill="none" />`
};

// Face expression type for hangman
export type HangmanFaceExpression = 'happy' | 'worrying' | 'scared' | 'sad';

// Get head part based on expression
const getHeadForExpression = (expression: HangmanFaceExpression): string => {
  switch (expression) {
    case 'happy':
      return HANGMAN_PARTS.headHappy;
    case 'worrying':
      return HANGMAN_PARTS.headWorrying;
    case 'scared':
      return HANGMAN_PARTS.headScared;
    case 'sad':
    default:
      return HANGMAN_PARTS.headSad;
  }
};

// Generate hangman SVG based on wrong guesses with face expression
export const generateHangmanSvg = (
  wrongGuesses: number, 
  isComplete = false, 
  faceExpression: HangmanFaceExpression = 'sad'
): string => {
  const headPart = wrongGuesses >= 2 || isComplete ? getHeadForExpression(faceExpression) : '';
  
  const parts = [
    HANGMAN_PARTS.gallows, // Always visible
    wrongGuesses >= 1 || isComplete ? HANGMAN_PARTS.rope : '',
    headPart,
    wrongGuesses >= 3 || isComplete ? HANGMAN_PARTS.body : '',
    wrongGuesses >= 4 || isComplete ? HANGMAN_PARTS.leftArm : '',
    wrongGuesses >= 5 || isComplete ? HANGMAN_PARTS.rightArm : '',
    wrongGuesses >= 6 || isComplete ? HANGMAN_PARTS.leftLeg : '',
    wrongGuesses >= 7 || isComplete ? HANGMAN_PARTS.rightLeg : ''
  ];
  
  return createSvgComponent(
    'hangman-display',
    '0 20 200 180',
    parts.join('\n'),
    { 'style': 'background-color: transparent !important; background: none !important;' }
  );
};

// Generate hangman SVG with timer-based face expression
export const generateTimerAwareHangmanSvg = (
  wrongGuesses: number, 
  isComplete: boolean, 
  timeRemaining: number
): string => {
  const faceExpression = match({ isComplete, wrongGuesses, timeRemaining })
    .when(
      ({ isComplete }: { isComplete: boolean }) => isComplete,
      () => 'sad' as const
    )
    .when(
      ({ wrongGuesses }: { wrongGuesses: number }) => wrongGuesses < 2,
      () => 'sad' as const
    )
    .when(
      ({ timeRemaining }: { timeRemaining: number }) => timeRemaining > 20,
      () => 'happy' as const
    )
    .when(
      ({ timeRemaining }: { timeRemaining: number }) => timeRemaining > 10,
      () => 'worrying' as const
    )
    .otherwise(() => 'scared' as const);
  
  return generateHangmanSvg(wrongGuesses, isComplete, faceExpression);
};

// Celebration SVG
export const createCelebrationSvg = (): string => {
  const celebrationContent = `
    <g class="confetti">
      <circle cx="50" cy="50" r="3" fill="gold" />
      <circle cx="150" cy="60" r="4" fill="#FF6B6B" />
      <path d="M70 40l5 5-5 5 5-5-5-5" stroke="#4ECDC4" stroke-width="2" />
      <path d="M130 30l5 5-5 5 5-5-5-5" stroke="#FF6B6B" stroke-width="2" />
      <path d="M40 120l5 5-5 5 5-5-5-5" stroke="#FFE66D" stroke-width="2" />
      <path d="M160 140l5 5-5 5 5-5-5-5" stroke="#4ECDC4" stroke-width="2" />
    </g>
    
    <g>
      <circle cx="100" cy="70" r="15" stroke="green" stroke-width="4" fill="none" />
      <path d="M92 75c0 0 5 5 16 0" stroke="green" stroke-width="2" stroke-linecap="round" />
      <circle cx="93" cy="65" r="2" fill="green" />
      <circle cx="107" cy="65" r="2" fill="green" />
    </g>
    
    <path d="M100 85v50" stroke="green" stroke-width="4" stroke-linecap="round" />
    <path d="M100 95l-30 -20" stroke="green" stroke-width="4" stroke-linecap="round" />
    <path d="M100 95l30 -20" stroke="green" stroke-width="4" stroke-linecap="round" />
    <path d="M100 135l-25 25" stroke="green" stroke-width="4" stroke-linecap="round" />
    <path d="M100 135l25 25" stroke="green" stroke-width="4" stroke-linecap="round" />
  `;
  
  return createSvgComponent('hangman-display', '0 20 200 180', celebrationContent);
};

// Status display component
export const createStatusDisplay = (state: GameState, gamesRemaining?: number): string => {
  const getStatusText = () => {
    switch (state.status) {
      case 'won':
        const gamesText = typeof gamesRemaining === 'number' && gamesRemaining >= 0 
          ? ` ${gamesRemaining} game${gamesRemaining === 1 ? '' : 's'} left!`
          : '';
        return `<span class="status">🎉 You got it! The word was "${state.word}".${gamesText}</span>`;
      case 'lost':
        const gamesTextLost = typeof gamesRemaining === 'number' && gamesRemaining >= 0 
          ? ` ${gamesRemaining} game${gamesRemaining === 1 ? '' : 's'} left!`
          : '';
        return `<span class="status">💀 Game Over! The word was "${state.word}".${gamesTextLost}</span>`;
      case 'playing':
        return `<span class="status">🎯 Guess the word!</span>`;
      default:
        return '';
    }
  };
  
  return `<div class="status-display">${getStatusText()}</div>`;
};

// Countdown timer component
export const createCountdownTimer = (state: GameState): string => {
  if (state.status !== 'playing') return '';
  
  return `
    <div class="countdown-timer" id="countdown-timer" 
         data-start-time="${state.startTime}" 
         data-time-limit="${state.timeLimit}" 
         data-game-id="${state.id}">
      <div class="countdown-label">Time left:</div>
      <div class="countdown-number time-normal" id="countdown-number">
        ${state.timeLimit}
      </div>
      <div class="countdown-label">seconds</div>
    </div>
  `;
};

// Word display component
export const createWordDisplay = (state: GameState): string => {
  const displayWord = state.word
    .split('')
    .map(letter => state.guessedLetters.has(letter.toUpperCase()) ? letter : '_')
    .join(' ');
  
  return `
    <div class="word-display" aria-live="polite">
      <div class="word-letters" aria-label="Current word: ${displayWord}">
        ${displayWord}
      </div>
    </div>
  `;
};

// Keyboard component
export const createKeyboard = (state: GameState): string => {
  const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');
  
  const createKeyButton = (letter: string): string => {
    const isGuessed = state.guessedLetters.has(letter);
    const isCorrect = state.word.toUpperCase().includes(letter);
    const isDisabled = state.status !== 'playing' || isGuessed;
    
    let buttonClass = 'key-button';
    if (isGuessed) {
      buttonClass += isCorrect ? ' correct' : ' incorrect';
    }
    if (isDisabled) {
      buttonClass += ' disabled';
    }
    
    return `
      <button
        class="${buttonClass}"
        data-letter="${letter}"
        ${isDisabled ? 'disabled' : ''}
        ${!isDisabled ? `hx-get="/guess/${letter}" hx-target="#main-content" hx-swap="innerHTML"` : ''}
        aria-label="Guess letter ${letter}"
      >
        ${letter}
      </button>
    `;
  };
  
  return `
    <div class="keyboard-container">
      <div class="keyboard" role="group" aria-label="Letter selection keyboard">
        ${alphabet.map(createKeyButton).join('')}
      </div>
    </div>
  `;
};

// Hint button component
export const createHintButton = (state: GameState): string => {
  if (state.status !== 'playing') return '';
  
  const canUseHint = state.hintsUsed < state.hintsAllowed;
  
  return `
    <div class="hint-container">
      <button
        class="hint-button ${!canUseHint ? 'disabled' : ''}"
        ${!canUseHint ? 'disabled' : ''}
        ${canUseHint ? 'hx-get="/hint" hx-target="#main-content" hx-swap="innerHTML"' : ''}
        aria-label="Get a hint"
      >
        💡 Hint (${state.hintsUsed}/${state.hintsAllowed})
      </button>
    </div>
  `;
};

// Daily limit status component
export const createDailyLimitStatus = (gamesPlayed: number, limit: number = 5): string => {
  const gamesRemaining = Math.max(0, limit - gamesPlayed);
  const isNearLimit = gamesRemaining <= 2;
  const isAtLimit = gamesRemaining === 0;
  
  if (isAtLimit) {
    return `
      <div class="daily-limit-status limit-reached">
        <span class="limit-icon">🚫</span>
        <span class="limit-text">Daily limit reached (${gamesPlayed}/${limit})</span>
      </div>
    `;
  }
  
  return `
    <div class="daily-limit-status ${isNearLimit ? 'near-limit' : ''}">
      <span class="limit-icon">${isNearLimit ? '⚠️' : '🎮'}</span>
      <span class="limit-text">${gamesRemaining} game${gamesRemaining === 1 ? '' : 's'} remaining today</span>
    </div>
  `;
};

