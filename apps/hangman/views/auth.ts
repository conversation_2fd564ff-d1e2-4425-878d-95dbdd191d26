export const loginPage = (error?: string): string => `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Hangman Game - Login</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="/static/styles.css">
</head>
<body>
  <div class="auth-container">
    <div class="auth-card">
      <h1 class="auth-title">🎮 Hangman Game</h1>
      <p class="auth-subtitle">
        Enter your email address to get started. New users will be automatically registered.
      </p>
      
      <div class="email-form">
        <input 
          type="email" 
          id="email" 
          class="email-input" 
          placeholder="Enter your email"
          autocomplete="email"
          title="Please enter a valid email address"
          required
        />
        <button id="loginBtn" class="auth-button">
          <span class="loading">⏳</span>
          <span class="text">Enter Game</span>
        </button>
      </div>

      ${error ? `<div class="error-message">${error}</div>` : ''}
      
      <div class="auth-info">
        <strong>🚀 Quick & Simple</strong><br>
        Just enter your email to start playing. If you win, it will be used to send you a GitHub Store coupon!
      </div>
    </div>
  </div>

  <script>
    const emailInput = document.getElementById('email');
    const loginBtn = document.getElementById('loginBtn');

    // Utility functions
    function showLoading(button) {
      button.disabled = true;
      button.querySelector('.loading').classList.add('active');
      button.querySelector('.text').style.opacity = '0.7';
    }

    function hideLoading(button) {
      button.disabled = false;
      button.querySelector('.loading').classList.remove('active');
      button.querySelector('.text').style.opacity = '1';
    }

    function showError(message) {
      const existing = document.querySelector('.error-message');
      if (existing) existing.remove();
      
      const error = document.createElement('div');
      error.className = 'error-message';
      error.textContent = message;
      document.querySelector('.auth-card').appendChild(error);
    }

    function showSuccess(message) {
      const existing = document.querySelector('.success-message');
      if (existing) existing.remove();
      
      const success = document.createElement('div');
      success.className = 'success-message';
      success.textContent = message;
      document.querySelector('.auth-card').appendChild(success);
    }

    // Email validation function
    function validateEmail(email) {
      const emailRegex = /^[a-zA-Z0-9]([a-zA-Z0-9._-]*[a-zA-Z0-9])?@[a-zA-Z0-9]([a-zA-Z0-9.-]*[a-zA-Z0-9])?\.[a-zA-Z]{2,}$/;
      return emailRegex.test(email);
    }

    // Login flow with auto-registration
    loginBtn.addEventListener('click', async () => {
      const email = emailInput.value.trim();
      if (!email) {
        showError('Please enter an email address');
        return;
      }
      
      if (!validateEmail(email)) {
        showError('Please enter a valid email address');
        return;
      }

      showLoading(loginBtn);

      try {
        const response = await fetch('/auth/login', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ username: email })
        });

        const result = await response.json();

        if (response.ok && result.success) {
          // Show success message briefly before redirect
          showSuccess(result.message);
          
          // Clear any existing error messages
          const errorDiv = document.querySelector('.error-message');
          if (errorDiv) errorDiv.remove();
          
          // Redirect to home page after a brief delay
          setTimeout(() => {
            window.location.href = '/';
          }, 1000);
        } else {
          showError(result.error || 'Login failed. Please try again.');
        }
      } catch (error) {
        console.error('Login error:', error);
        showError('Login failed. Please check your connection and try again.');
      } finally {
        hideLoading(loginBtn);
      }
    });

    // Enter key support
    emailInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        loginBtn.click();
      }
    });

    // Focus on email input when page loads
    document.addEventListener('DOMContentLoaded', () => {
      emailInput.focus();
    });
  </script>

  <style>
    /* Updated auth page styles for login-only authentication */
    .auth-container {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 1rem;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    }

    .auth-card {
      background: white;
      border-radius: 1rem;
      padding: 2rem;
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
      width: 100%;
      max-width: 400px;
      text-align: center;
    }

    .auth-title {
      color: var(--primary-color);
      margin: 0 0 1rem 0;
      font-size: 2rem;
      font-weight: 700;
    }

    .auth-subtitle {
      color: #666;
      margin: 0 0 2rem 0;
      line-height: 1.5;
    }

    .email-form {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .email-input {
      padding: 0.75rem;
      border: 2px solid #e0e0e0;
      border-radius: 0.5rem;
      font-size: 1rem;
      transition: border-color 0.2s;
    }

    .email-input:focus {
      outline: none;
      border-color: var(--secondary-color);
    }

    .auth-button {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 0.5rem;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      background: var(--secondary-color);
      color: white;
    }

    .auth-button:hover:not(:disabled) {
      background: #2980b9;
    }

    .auth-button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .loading {
      display: none;
    }

    .loading.active {
      display: inline;
    }

    .error-message {
      background: #fee;
      color: #c00;
      padding: 0.75rem;
      border-radius: 0.5rem;
      margin: 1rem 0;
      border: 1px solid #fcc;
    }

    .success-message {
      background: #efe;
      color: #060;
      padding: 0.75rem;
      border-radius: 0.5rem;
      margin: 1rem 0;
      border: 1px solid #cfc;
    }

    .auth-info {
      background: #f8f9fa;
      padding: 1rem;
      border-radius: 0.5rem;
      color: #666;
      font-size: 0.9rem;
      line-height: 1.4;
    }

    @media (max-width: 480px) {
      .auth-card {
        padding: 1.5rem;
      }
      
      .auth-title {
        font-size: 1.5rem;
      }
    }
  </style>
</body>
</html>
`;