# Production User Management

This document describes how to manage users in the production environment using curl commands.

## Prerequisites

1. **Admin Key**: You need the `ADMIN_KEY` environment variable set in production
2. **Production URL**: Replace `https://hangman.deno.dev` with your actual production URL

## Available API Endpoints

### 1. List All Users

```bash
curl -X GET "https://hangman.deno.dev/admin/users" \
  -H "Authorization: Bearer YOUR_ADMIN_KEY" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "success": true,
  "users": [
    {
      "username": "<EMAIL>",
      "displayName": "User Name",
      "createdAt": "2025-07-13T12:00:00.000Z"
    }
  ],
  "count": 1
}
```

### 2. Get User Details

```bash
curl -X GET "https://hangman.deno.dev/admin/users/<EMAIL>" \
  -H "Authorization: Bearer YOUR_ADMIN_KEY" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "success": true,
  "user": {
    "username": "<EMAIL>",
    "displayName": "User Name",
    "createdAt": "2025-07-13T12:00:00.000Z",
    "statistics": {
      "gamesPlayed": 5,
      "gamesWon": 3,
      "currentStreak": 2,
      "bestStreak": 3,
      "totalGuesses": 25,
      "averageGuessesPerWin": 8,
      "totalHintsUsed": 2
    },
    "hasDailyData": true,
    "winRecordCount": 3,
    "activeSessionCount": 0
  }
}
```

### 3. Remove User (Dry Run)

**Preview what would be deleted without actually removing anything:**

```bash
curl -X DELETE "https://hangman.deno.dev/admin/users/<EMAIL>/remove?dry-run=true" \
  -H "Authorization: Bearer YOUR_ADMIN_KEY" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "success": true,
  "dryRun": true,
  "message": "DRY RUN: Would <NAME_EMAIL>",
  "estimatedDeletionCount": 6,
  "itemsToDelete": [
    "user_profile",
    "user_statistics",
    "daily_games",
    "3_win_records",
    "2_standings",
    "1_word_history"
  ]
}
```

### 4. Remove User (Permanent)

⚠️ **WARNING: This permanently deletes all user data from ALL KV stores!**

**Complete Data Removal Includes:**
- User profile and authentication data
- Game statistics and win records  
- Daily game limits and session data
- Standings/leaderboard entries
- Word history and game progress
- Magic link tokens

```bash
curl -X DELETE "https://hangman.deno.dev/admin/users/<EMAIL>/remove" \
  -H "Authorization: Bearer YOUR_ADMIN_KEY" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "success": true,
  "message": "User <EMAIL> has been permanently removed",
  "deletionCount": 8,
  "deletedItems": [
    "user_profile",
    "user_statistics", 
    "daily_games",
    "hangman_daily_games",
    "3_win_records",
    "1_active_sessions",
    "2_standings",
    "1_word_history"
  ]
}
```

## Environment Setup

### Production Environment Variables

Make sure these are set in your Deno Deploy environment:

```bash
# Required for admin API access
ADMIN_KEY=your-secure-random-key-here

# Optional: For enhanced admin logging  
LOG_LEVEL=INFO
```

### Generate Admin API Key

```bash
# Generate a secure random key
openssl rand -hex 32

# Or use Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"

# Or use online generator
# https://generate-random.org/api-key-generator
```

## Quick Command Examples

### Remove a problematic user
```bash
# 1. First check if user exists
curl -X GET "https://hangman.deno.dev/admin/users/<EMAIL>" \
  -H "Authorization: Bearer YOUR_ADMIN_KEY"

# 2. Preview what would be deleted (dry run)
curl -X DELETE "https://hangman.deno.dev/admin/users/<EMAIL>/remove?dry-run=true" \
  -H "Authorization: Bearer YOUR_ADMIN_KEY"

# 3. Remove the user (after confirming dry run results)
curl -X DELETE "https://hangman.deno.dev/admin/users/<EMAIL>/remove" \
  -H "Authorization: Bearer YOUR_ADMIN_KEY"
```

### Bulk user information
```bash
# Get all users and save to file
curl -X GET "https://hangman.deno.dev/admin/users" \
  -H "Authorization: Bearer YOUR_ADMIN_KEY" \
  > production_users.json
```

### Check user statistics before removal
```bash
# Get detailed user info
curl -X GET "https://hangman.deno.dev/admin/users/<EMAIL>" \
  -H "Authorization: Bearer YOUR_ADMIN_KEY" | jq '.user.statistics'
```

## Error Responses

### 401 Unauthorized
```json
{
  "success": false,
  "error": "Unauthorized"
}
```
**Solution**: Check your `ADMIN_KEY` is correct and set in production.

### 404 User Not Found
```json
{
  "success": false,
  "error": "User not found"
}
```
**Solution**: Verify the email address is correct.

### 500 Internal Server Error
```json
{
  "success": false,
  "error": "Failed to remove user"
}
```
**Solution**: Check server logs for detailed error information.

## Security Features

- ✅ **API Key Authentication**: Requires valid admin API key
- ✅ **Audit Logging**: All admin actions are logged with IP and user agent
- ✅ **No Undo**: Prevents accidental bulk operations
- ✅ **Detailed Response**: Shows exactly what was deleted
- ✅ **HTTPS Only**: All API calls must use HTTPS in production

## Alternative: Local Script with Production KV

If you prefer running the script locally but against production data:

```bash
# Set production KV connection
export DENO_KV_URL="your-production-kv-url"

# Run local script against production data (comprehensive removal)
deno task user:remove-dry <EMAIL>    # Dry run to see what would be deleted
deno task user:remove <EMAIL>        # Actual removal from all KV stores
```

**Local Script Features:**
- **Comprehensive removal** from all KV stores (same as API)
- **Dry run mode** to preview what will be deleted
- **Interactive confirmation** for safety
- **Detailed logging** of each deletion step

## Monitoring and Logging

All admin actions are logged with:
- Target user email
- Admin request details (IP, user agent)
- Deletion results
- Timestamp

Check your production logs for entries like:
```
[WARN] [API] Admin user removal initiated | Data: {"targetUser":"<EMAIL>","adminRequest":{"userAgent":"curl/7.68.0","ip":"*******"}}
```

## Best Practices

1. **Always check user details first** before removal
2. **Keep admin key secure** and rotate regularly
3. **Document reason for removal** in your admin logs
4. **Verify user is actually problematic** before removing
5. **Use HTTPS only** for all admin API calls
6. **Monitor admin action logs** for security auditing