#!/usr/bin/env deno run --allow-env --unstable-kv

/**
 * KV Store Utilities
 * 
 * A collection of utilities for managing the Deno KV store during development.
 * 
 * Usage:
 *   deno run --allow-env --unstable-kv scripts/kv-utils.ts <command>
 * 
 * Commands:
 *   list        - List all entries in the KV store
 *   clear       - Clear all data (with confirmation)
 *   clear-users - Clear only user data
 *   clear-games - Clear only game data (stats, wins, standings)
 *   clear-sessions - Clear only active sessions
 *   stats       - Show database statistics
 */

async function listAllEntries() {
  const kv = await Deno.openKv();
  console.log("📋 Listing all KV store entries:\n");
  
  const categories = new Map<string, number>();
  let total = 0;
  
  for await (const { key, value } of kv.list({ prefix: [] })) {
    const prefix = key[0] as string;
    categories.set(prefix, (categories.get(prefix) || 0) + 1);
    total++;
    
    try {
      const jsonValue = JSON.stringify(value, (_, v) => typeof v === 'bigint' ? v.toString() : v);
      console.log(`${key.join("/")} = ${jsonValue.substring(0, 100)}${jsonValue.length > 100 ? "..." : ""}`);
    } catch (error) {
      console.log(`${key.join("/")} = [Cannot serialize: ${error instanceof Error ? error.message : 'Unknown error'}]`);
    }
  }
  
  console.log(`\n📊 Summary:`);
  console.log(`Total entries: ${total}`);
  for (const [category, count] of categories.entries()) {
    console.log(`  ${category}: ${count} entries`);
  }
  
  kv.close();
}

async function clearByPrefix(prefixes: string[][], description: string) {
  const kv = await Deno.openKv();
  console.log(`🧹 Clearing ${description}...`);
  
  let totalDeleted = 0;
  
  for (const prefix of prefixes) {
    let deletedCount = 0;
    
    for await (const { key } of kv.list({ prefix })) {
      await kv.delete(key);
      deletedCount++;
      totalDeleted++;
    }
    
    if (deletedCount > 0) {
      console.log(`✅ Deleted ${deletedCount} entries with prefix: ${prefix.join("/")}`);
    }
  }
  
  console.log(`🎉 Cleared ${description}! Total entries deleted: ${totalDeleted}`);
  kv.close();
}

async function showStats() {
  const kv = await Deno.openKv();
  console.log("📊 KV Store Statistics:\n");
  
  const stats = new Map<string, number>();
  let total = 0;
  
  for await (const { key } of kv.list({ prefix: [] })) {
    const prefix = key[0] as string;
    stats.set(prefix, (stats.get(prefix) || 0) + 1);
    total++;
  }
  
  console.log(`Total entries: ${total}`);
  console.log("");
  
  if (total === 0) {
    console.log("🧹 KV store is empty");
    kv.close();
    return;
  }
  
  // Sort by count (descending)
  const sortedStats = Array.from(stats.entries()).sort((a, b) => b[1] - a[1]);
  
  for (const [category, count] of sortedStats) {
    const percentage = ((count / total) * 100).toFixed(1);
    console.log(`${category.padEnd(15)} ${count.toString().padStart(4)} entries (${percentage}%)`);
  }
  
  // Show some sample data
  if (stats.get("user")) {
    console.log("\n👤 Sample users:");
    let userCount = 0;
    for await (const { key, value } of kv.list({ prefix: ["user"] })) {
      if (userCount >= 3) break;
      const user = value as any;
      console.log(`  ${user.username} - ${user.credentials?.length || 0} credentials`);
      userCount++;
    }
    if (stats.get("user")! > 3) {
      console.log(`  ... and ${stats.get("user")! - 3} more users`);
    }
  }
  
  if (stats.get("wins")) {
    console.log("\n🏆 Recent wins:");
    let winCount = 0;
    const wins = [];
    for await (const { value } of kv.list({ prefix: ["wins"] }, { reverse: true, limit: 3 })) {
      const win = value as any;
      wins.push(`  #${win.sequenceNumber}: ${win.username} - "${win.word}" (${win.duration}s)`);
      winCount++;
    }
    wins.forEach(w => console.log(w));
  }
  
  kv.close();
}

// Main command handler
const command = Deno.args[0];

switch (command) {
  case "list":
    await listAllEntries();
    break;
    
  case "clear":
    const confirmation = prompt("⚠️  This will delete ALL data. Type 'YES' to confirm: ");
    if (confirmation === "YES") {
      await clearByPrefix([
        ["user"], ["session"], ["challenge"], ["user_stats"], 
        ["wins"], ["user_wins"], ["standings"], ["daily_games"], ["global"]
      ], "all data");
    } else {
      console.log("❌ Operation cancelled.");
    }
    break;
    
  case "clear-users":
    await clearByPrefix([["user"], ["session"], ["challenge"]], "user accounts and sessions");
    break;
    
  case "clear-games":
    await clearByPrefix([["user_stats"], ["wins"], ["user_wins"], ["standings"], ["daily_games"], ["global"]], "game data");
    break;
    
  case "clear-sessions":
    await clearByPrefix([["session"], ["challenge"]], "active sessions");
    break;
    
  case "stats":
    await showStats();
    break;
    
  default:
    console.log("KV Store Utilities\n");
    console.log("Usage: deno run --allow-env --unstable-kv scripts/kv-utils.ts <command>\n");
    console.log("Commands:");
    console.log("  list          - List all entries in the KV store");
    console.log("  clear         - Clear all data (with confirmation)");
    console.log("  clear-users   - Clear only user data");
    console.log("  clear-games   - Clear only game data (stats, wins, standings)");
    console.log("  clear-sessions - Clear only active sessions");
    console.log("  stats         - Show database statistics");
    console.log("\nExamples:");
    console.log("  deno run --allow-env --unstable-kv scripts/kv-utils.ts stats");
    console.log("  deno run --allow-env --unstable-kv scripts/kv-utils.ts clear-sessions");
}

// Export functions for use by admin routes
export async function getKvStats() {
  const kv = await Deno.openKv();
  
  const stats = new Map<string, number>();
  let total = 0;
  const sampleData: any = {};
  
  for await (const { key, value } of kv.list({ prefix: [] })) {
    const prefix = key[0] as string;
    stats.set(prefix, (stats.get(prefix) || 0) + 1);
    total++;
    
    // Collect sample data
    if (!sampleData[prefix] && prefix === "user") {
      const user = value as any;
      sampleData[prefix] = {
        username: user.username,
        credentials: user.credentials?.length || 0
      };
    }
  }
  
  kv.close();
  
  return {
    total,
    categories: Object.fromEntries(stats),
    sampleData
  };
}

export async function clearKvData(type: string = "all"): Promise<{ deletedCount: number }> {
  const prefixMap: Record<string, string[][]> = {
    all: [["user"], ["session"], ["challenge"], ["user_stats"], ["wins"], ["user_wins"], ["standings"], ["daily_games"], ["global"]],
    users: [["user"], ["session"], ["challenge"]],
    games: [["user_stats"], ["wins"], ["user_wins"], ["standings"], ["daily_games"], ["global"]],
    sessions: [["session"], ["challenge"]]
  };
  
  const prefixes = prefixMap[type];
  if (!prefixes) {
    throw new Error(`Invalid type: ${type}. Available: ${Object.keys(prefixMap).join(", ")}`);
  }
  
  const kv = await Deno.openKv();
  let totalDeleted = 0;
  
  for (const prefix of prefixes) {
    for await (const { key } of kv.list({ prefix })) {
      await kv.delete(key);
      totalDeleted++;
    }
  }
  
  kv.close();
  
  return { deletedCount: totalDeleted };
}