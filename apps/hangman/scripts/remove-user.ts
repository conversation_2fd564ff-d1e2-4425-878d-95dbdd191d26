#!/usr/bin/env deno run --allow-env --unstable-kv

/**
 * User Removal <PERSON>
 * 
 * Removes individual users from the hangman game database and prevents them
 * from appearing in standings or being able to authenticate.
 * 
 * Usage:
 *   deno run --allow-env --unstable-kv scripts/remove-user.ts <email>
 *   deno run --allow-env --unstable-kv scripts/remove-user.ts --list-users
 *   deno run --allow-env --unstable-kv scripts/remove-user.ts --dry-run <email>
 * 
 * Examples:
 *   deno run --allow-env --unstable-kv scripts/remove-user.ts <EMAIL>
 *   deno run --allow-env --unstable-kv scripts/remove-user.ts --dry-run <EMAIL>
 *   deno run --allow-env --unstable-kv scripts/remove-user.ts --list-users
 */

// Simple CLI logger for scripts
const CliLogger = {
  info: (message: string, data?: unknown): void => {
    console.log(`[INFO] ${message}`, data ? `| ${JSON.stringify(data)}` : '');
  },
  
  warn: (message: string, data?: unknown): void => {
    console.warn(`[WARN] ⚠️  ${message}`, data ? `| ${JSON.stringify(data)}` : '');
  },
  
  error: (message: string, data?: unknown): void => {
    console.error(`[ERROR] ❌ ${message}`, data ? `| ${JSON.stringify(data)}` : '');
  },
  
  success: (message: string, data?: unknown): void => {
    console.log(`[SUCCESS] ✅ ${message}`, data ? `| ${JSON.stringify(data)}` : '');
  },
  
  progress: (message: string, current: number, total: number): void => {
    const percentage = Math.round((current / total) * 100);
    console.log(`[PROGRESS] 🔄 ${message} (${current}/${total} - ${percentage}%)`);
  }
};

interface UserData {
  id: Uint8Array;
  username: string;
  displayName: string;
  createdAt: number;
}

interface UserStatistics {
  gamesPlayed: number;
  gamesWon: number;
  currentStreak: number;
  bestStreak: number;
  totalGuesses: number;
  averageGuessesPerWin: number;
  totalHintsUsed?: number;
}

async function listAllUsers() {
  const kv = await Deno.openKv();
  CliLogger.info("Listing all users in the database");
  
  const users: Array<{ username: string; displayName: string; createdAt: string }> = [];
  
  for await (const { key, value } of kv.list({ prefix: ["user"] })) {
    if (key.length === 2 && typeof key[1] === "string") {
      const userData = value as UserData;
      users.push({
        username: userData.username,
        displayName: userData.displayName,
        createdAt: new Date(userData.createdAt).toISOString()
      });
    }
  }
  
  if (users.length === 0) {
    CliLogger.info("No users found in the database");
  } else {
    CliLogger.info(`Found ${users.length} users:`);
    users.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.username} (${user.displayName}) - Created: ${user.createdAt}`);
    });
  }
  
  kv.close();
}

async function getUserData(username: string) {
  const kv = await Deno.openKv();
  
  try {
    // Get user data
    const userResult = await kv.get<UserData>(["user", username]);
    const userData = userResult.value;
    
    // Get user statistics
    const statsResult = await kv.get<UserStatistics>(["user_statistics", username]);
    const userStats = statsResult.value;
    
    // Get daily game data
    const dailyResult = await kv.get(["daily_games", username]);
    const dailyData = dailyResult.value;
    
    // Get user's win records
    const winRecords: any[] = [];
    for await (const { key, value } of kv.list({ prefix: ["win_record"] })) {
      const record = value as any;
      if (record.username === username) {
        winRecords.push(record);
      }
    }
    
    // Get user's active sessions
    const sessions: string[] = [];
    for await (const { key, value } of kv.list({ prefix: ["session"] })) {
      const gameState = value as any;
      if (gameState.username === username) {
        sessions.push(key[1] as string);
      }
    }
    
    return {
      userData,
      userStats,
      dailyData,
      winRecords,
      sessions,
      exists: !!userData
    };
  } finally {
    kv.close();
  }
}

async function removeUser(username: string, dryRun: boolean = false) {
  const kv = await Deno.openKv();
  
  try {
    CliLogger.info(`${dryRun ? '[DRY RUN] ' : ''}Starting user removal process for: ${username}`);
    
    // Get user data first (but continue even if not found in main store)
    const userData = await getUserData(username);
    
    if (!userData.exists) {
      CliLogger.warn(`User not found in main store: ${username}`);
      CliLogger.info(`Continuing to check and clean other KV stores...`);
    } else {
      CliLogger.info(`Found user data for: ${username}`);
      console.log(`  Display Name: ${userData.userData?.displayName}`);
      console.log(`  Statistics: ${userData.userStats ? 'Yes' : 'No'}`);
      console.log(`  Win Records: ${userData.winRecords.length}`);
      console.log(`  Active Sessions: ${userData.sessions.length}`);
      console.log(`  Daily Data: ${userData.dailyData ? 'Yes' : 'No'}`);
    }
    
    if (dryRun) {
      CliLogger.info("DRY RUN: Would remove the following data:");
      if (userData.exists) {
        console.log(`  - User profile: ["user", "${username}"]`);
        console.log(`  - User statistics: ["user_statistics", "${username}"]`);
        console.log(`  - Daily games: ["daily_games", "${username}"]`);
        console.log(`  - Win records: ${userData.winRecords.length} records`);
        console.log(`  - Active sessions: ${userData.sessions.length} sessions`);
      } else {
        console.log(`  - User profile: Not found`);
        console.log(`  - User statistics: Not found`); 
        console.log(`  - Daily games: Not found`);
        console.log(`  - Win records: Not found in main store`);
        console.log(`  - Active sessions: Not found in main store`);
      }
      console.log(`  - Magic link tokens: (checking all KV stores...)`);
      console.log(`  - Standings entries: (checking all KV stores...)`);
      console.log(`  - Hangman daily games: ["hangman_daily_games", "${username}"]`);
      console.log(`  - Hangman word history: (checking all KV stores...)`);
      return;
    }
    
    // Confirm removal
    if (userData.exists) {
      console.log("\n⚠️  WARNING: This will permanently delete all data for this user!");
    } else {
      console.log("\n⚠️  WARNING: Will scan and clean any remaining data for this user across all KV stores!");
    }
    console.log("This action cannot be undone.");
    
    const confirmation = prompt("Type 'DELETE' to confirm removal: ");
    if (confirmation !== "DELETE") {
      CliLogger.info("User removal cancelled");
      return;
    }
    
    CliLogger.info("Proceeding with user removal...");
    
    let deletionCount = 0;
    
    // Remove user profile
    if (userData.userData) {
      await kv.delete(["user", username]);
      deletionCount++;
      CliLogger.success("Deleted user profile");
    }
    
    // Remove user statistics
    if (userData.userStats) {
      await kv.delete(["user_statistics", username]);
      deletionCount++;
      CliLogger.success("Deleted user statistics");
    }
    
    // Remove daily game data
    if (userData.dailyData) {
      await kv.delete(["daily_games", username]);
      deletionCount++;
      CliLogger.success("Deleted daily game data");
    }
    
    // Remove win records
    for (const record of userData.winRecords) {
      const recordKey = ["win_record", record.sequenceNumber];
      await kv.delete(recordKey);
      deletionCount++;
    }
    if (userData.winRecords.length > 0) {
      CliLogger.success(`Deleted ${userData.winRecords.length} win records`);
    }
    
    // Remove active sessions
    for (const sessionId of userData.sessions) {
      await kv.delete(["session", sessionId]);
      deletionCount++;
    }
    if (userData.sessions.length > 0) {
      CliLogger.success(`Deleted ${userData.sessions.length} active sessions`);
    }
    
    // Remove any magic link tokens for this user
    let magicLinkCount = 0;
    for await (const { key, value } of kv.list({ prefix: ["magic_link"] })) {
      const linkData = value as any;
      if (linkData.username === username) {
        await kv.delete(key);
        magicLinkCount++;
      }
    }
    if (magicLinkCount > 0) {
      CliLogger.success(`Deleted ${magicLinkCount} magic link tokens`);
      deletionCount += magicLinkCount;
    }
    
    // Remove standings entries
    let standingsCount = 0;
    for await (const { key, value } of kv.list({ prefix: ["standings"] })) {
      const standing = value as any;
      if (standing.username === username) {
        await kv.delete(key);
        standingsCount++;
      }
    }
    if (standingsCount > 0) {
      CliLogger.success(`Deleted ${standingsCount} standings entries`);
      deletionCount += standingsCount;
    }
    
    // Remove hangman daily games
    const hangmanDailyResult = await kv.get(["hangman_daily_games", username]);
    if (hangmanDailyResult.value) {
      await kv.delete(["hangman_daily_games", username]);
      deletionCount++;
      CliLogger.success("Deleted hangman daily games data");
    }
    
    // Remove hangman word history
    let wordHistoryCount = 0;
    for await (const { key, value } of kv.list({ prefix: ["hangman_word_history"] })) {
      const history = value as any;
      if (history.username === username || (Array.isArray(history) && history.some((entry: any) => entry.username === username))) {
        await kv.delete(key);
        wordHistoryCount++;
      }
    }
    if (wordHistoryCount > 0) {
      CliLogger.success(`Deleted ${wordHistoryCount} word history entries`);
      deletionCount += wordHistoryCount;
    }
    
    CliLogger.success(`User removal/cleanup completed successfully!`);
    CliLogger.success(`Total items deleted: ${deletionCount}`);
    if (userData.exists) {
      CliLogger.info(`User ${username} has been completely removed from all systems`);
    } else {
      CliLogger.info(`Cleaned up any remaining traces of ${username} from all KV stores`);
    }
    
  } catch (error) {
    CliLogger.error("Error during user removal", { 
      username, 
      error: error instanceof Error ? error.message : String(error) 
    });
    throw error;
  } finally {
    kv.close();
  }
}

async function showUserDetails(username: string) {
  CliLogger.info(`Retrieving detailed information for: ${username}`);
  
  const userData = await getUserData(username);
  
  if (!userData.exists) {
    CliLogger.warn(`User not found: ${username}`);
    return;
  }
  
  console.log("\n=== USER DETAILS ===");
  console.log(`Username: ${userData.userData?.username}`);
  console.log(`Display Name: ${userData.userData?.displayName}`);
  console.log(`Created At: ${userData.userData ? new Date(userData.userData.createdAt).toISOString() : 'N/A'}`);
  
  if (userData.userStats) {
    console.log("\n=== STATISTICS ===");
    console.log(`Games Played: ${userData.userStats.gamesPlayed}`);
    console.log(`Games Won: ${userData.userStats.gamesWon}`);
    console.log(`Current Streak: ${userData.userStats.currentStreak}`);
    console.log(`Best Streak: ${userData.userStats.bestStreak}`);
    console.log(`Total Guesses: ${userData.userStats.totalGuesses}`);
    console.log(`Average Guesses Per Win: ${userData.userStats.averageGuessesPerWin}`);
    console.log(`Total Hints Used: ${userData.userStats.totalHintsUsed || 0}`);
  }
  
  if (userData.dailyData) {
    console.log("\n=== DAILY DATA ===");
    console.log(`Daily data exists: Yes`);
  }
  
  if (userData.winRecords.length > 0) {
    console.log("\n=== WIN RECORDS ===");
    console.log(`Total win records: ${userData.winRecords.length}`);
    userData.winRecords.slice(0, 5).forEach((record, index) => {
      console.log(`  ${index + 1}. Word: ${record.word}, Time: ${record.duration}s, Hints: ${record.hintsUsed}`);
    });
    if (userData.winRecords.length > 5) {
      console.log(`  ... and ${userData.winRecords.length - 5} more records`);
    }
  }
  
  if (userData.sessions.length > 0) {
    console.log("\n=== ACTIVE SESSIONS ===");
    console.log(`Active sessions: ${userData.sessions.length}`);
    userData.sessions.forEach((sessionId, index) => {
      console.log(`  ${index + 1}. ${sessionId}`);
    });
  }
}

// Main execution
async function main() {
  const args = Deno.args;
  
  if (args.length === 0) {
    console.log("Usage:");
    console.log("  deno run --allow-env --unstable-kv scripts/remove-user.ts <email>");
    console.log("  deno run --allow-env --unstable-kv scripts/remove-user.ts --list-users");
    console.log("  deno run --allow-env --unstable-kv scripts/remove-user.ts --dry-run <email>");
    console.log("  deno run --allow-env --unstable-kv scripts/remove-user.ts --details <email>");
    console.log("");
    console.log("Examples:");
    console.log("  deno run --allow-env --unstable-kv scripts/remove-user.ts <EMAIL>");
    console.log("  deno run --allow-env --unstable-kv scripts/remove-user.ts --dry-run <EMAIL>");
    console.log("  deno run --allow-env --unstable-kv scripts/remove-user.ts --list-users");
    Deno.exit(1);
  }
  
  try {
    if (args[0] === "--list-users") {
      await listAllUsers();
    } else if (args[0] === "--dry-run" && args[1]) {
      await removeUser(args[1], true);
    } else if (args[0] === "--details" && args[1]) {
      await showUserDetails(args[1]);
    } else if (args[0] && !args[0].startsWith("--")) {
      await removeUser(args[0], false);
    } else {
      CliLogger.error("Invalid arguments");
      Deno.exit(1);
    }
  } catch (error) {
    CliLogger.error("Script execution failed", { 
      error: error instanceof Error ? error.message : String(error) 
    });
    Deno.exit(1);
  }
}

if (import.meta.main) {
  await main();
}