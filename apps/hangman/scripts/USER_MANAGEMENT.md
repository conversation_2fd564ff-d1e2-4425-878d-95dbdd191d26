# User Management Scripts

This document describes the user management tools available for the Hangman game.

## Available Commands

### List All Users
```bash
deno task user:list
```
Shows all registered users with their display names and creation dates.

### View User Details
```bash
deno task user:details <email>
```
Shows detailed information about a specific user including:
- Profile information
- Game statistics
- Win records
- Active sessions
- Daily game data

**Example:**
```bash
deno task user:details <EMAIL>
```

### Remove User (Dry Run)
```bash
deno task user:remove-dry <email>
```
Shows what would be deleted without actually removing anything. Safe to run for testing.

**Example:**
```bash
deno task user:remove-dry <EMAIL>
```

### Remove User (Permanent)
```bash
deno task user:remove <email>
```
**⚠️ WARNING: This permanently deletes all user data!**

This command will:
1. Show what will be deleted
2. Ask for confirmation (you must type "DELETE")
3. Remove all user data including:
   - User profile
   - Game statistics
   - Win records
   - Active sessions
   - Daily game data
   - Magic link tokens

**Example:**
```bash
deno task user:remove <EMAIL>
```

## What Gets Removed

When a user is removed, the following data is permanently deleted:

### User Profile
- Basic user information (username, display name, creation date)
- Stored in KV key: `["user", "<EMAIL>"]`

### Game Statistics
- Games played, won, streaks, averages
- Stored in KV key: `["user_statistics", "<EMAIL>"]`

### Win Records
- Individual game completion records
- Stored in KV keys: `["win_record", sequenceNumber]`

### Active Sessions
- Any currently active game sessions
- Stored in KV keys: `["session", "sessionId"]`

### Daily Game Data
- Daily game limits and counts
- Stored in KV key: `["daily_games", "<EMAIL>"]`

### Magic Link Tokens
- Any active authentication tokens
- Stored in KV keys: `["magic_link", "tokenId"]`

## Effects of User Removal

After removal:
- ✅ User will no longer appear in standings/leaderboards
- ✅ User cannot authenticate or log in
- ✅ All game history is removed
- ✅ User can re-register with the same email (starts fresh)
- ❌ Action cannot be undone

## Safety Features

- **Dry Run Mode**: Test removal without actual deletion
- **Confirmation Required**: Must type "DELETE" to confirm
- **Detailed Logging**: Shows exactly what will be removed
- **User Details**: View user data before deciding to remove

## Use Cases

### Moderation
Remove users who violate terms of service or game rules.

### Data Privacy
Honor user requests to delete their data (GDPR compliance).

### Testing/Development
Clean up test accounts or corrupted user data.

### Account Reset
Help users start fresh if their account has issues.

## Examples

```bash
# Check what users exist
deno task user:list

# Get details about a specific user
deno task user:details <EMAIL>

# Test what would be removed (safe)
deno task user:remove-dry <EMAIL>

# Actually remove the user (permanent)
deno task user:remove <EMAIL>
```

## Best Practices

1. **Always run dry-run first** to see what will be affected
2. **Check user details** to understand the impact
3. **Document the reason** for removal (for your records)
4. **Inform the user** if this is a moderation action
5. **Double-check the email** before confirming removal

## Troubleshooting

### "User not found"
- Check the email spelling
- Use `deno task user:list` to see available users

### "Permission denied"
- Ensure you have KV access permissions
- Check that the script has `--unstable-kv` flag

### Script errors
- Check that Deno KV is available
- Verify the database is accessible
- Ensure proper file permissions