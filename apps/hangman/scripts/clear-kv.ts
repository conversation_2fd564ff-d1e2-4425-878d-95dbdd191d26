#!/usr/bin/env deno run --allow-env --unstable-kv

/**
 * KV Store Cleanup Script
 * 
 * This script clears all authentication and game data from the Deno KV store.
 * Use this when you want to start fresh with a clean database.
 * 
 * Usage:
 *   deno run --allow-env --unstable-kv scripts/clear-kv.ts
 * 
 * What gets cleared:
 * - All user accounts and credentials
 * - All active sessions
 * - All authentication challenges
 * - All game statistics
 * - All win records
 * - All daily game limits
 * - All player standings
 */

async function clearKvStore() {
  console.log("🧹 Starting KV store cleanup...");
  
  const kv = await Deno.openKv();
  
  const prefixesToClear = [
    ["user"],           // User accounts and credentials
    ["session"],        // Active sessions
    ["challenge"],      // WebAuthn challenges
    ["user_stats"],     // User game statistics
    ["wins"],           // Win records
    ["user_wins"],      // User-specific win records
    ["standings"],      // Player leaderboard standings
    ["daily_games"],    // Daily game limits
    ["global"],         // Global counters (win sequence, etc.)
  ];
  
  let totalDeleted = 0;
  
  for (const prefix of prefixesToClear) {
    console.log(`\n🔍 Clearing entries with prefix: ${prefix.join("/")}`);
    let deletedCount = 0;
    
    // List all entries with this prefix
    for await (const { key } of kv.list({ prefix })) {
      await kv.delete(key);
      deletedCount++;
      totalDeleted++;
      
      // Show progress for large deletions
      if (deletedCount % 10 === 0) {
        Deno.stdout.writeSync(new TextEncoder().encode("."));
      }
    }
    
    console.log(`\n✅ Deleted ${deletedCount} entries with prefix: ${prefix.join("/")}`);
  }
  
  console.log(`\n🎉 KV store cleanup complete!`);
  console.log(`📊 Total entries deleted: ${totalDeleted}`);
  console.log(`\n🚀 Your application now has a clean slate!`);
  
  kv.close();
}

async function confirmClearance() {
  console.log("⚠️  WARNING: This will permanently delete ALL data from the KV store!");
  console.log("This includes:");
  console.log("  - All user accounts and WebAuthn credentials");
  console.log("  - All game statistics and leaderboards");
  console.log("  - All session data");
  console.log("  - All win records");
  console.log("");
  
  const confirmation = prompt("Are you sure you want to continue? Type 'YES' to confirm: ");
  
  if (confirmation === "YES") {
    await clearKvStore();
  } else {
    console.log("❌ Operation cancelled. No data was deleted.");
  }
}

// Check if running with --force flag to skip confirmation
const args = Deno.args;
if (args.includes("--force")) {
  console.log("🔥 Force mode enabled - skipping confirmation...");
  await clearKvStore();
} else {
  await confirmClearance();
}