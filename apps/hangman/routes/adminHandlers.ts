/**
 * Admin API handlers for user management
 * Provides secure endpoints for administrative tasks
 */

import { createJsonResponse } from "../../../libs/utils/http.ts";
import { createErrorResponse } from "../../../libs/utils/errors.ts";
import { Logger, LogContext } from "../../../libs/utils/logger.ts";

// Admin key (should be set via environment variable)
const ADMIN_KEY = Deno.env.get("ADMIN_KEY");

/**
 * Authenticate admin requests
 */
function authenticateAdmin(request: Request): boolean {
  if (!ADMIN_KEY) {
    Logger.warn(LogContext.API, "Admin key not configured");
    return false;
  }

  const authHeader = request.headers.get("Authorization");
  const providedKey = authHeader?.replace("Bearer ", "");
  
  return providedKey === ADMIN_KEY;
}

/**
 * Get user data for admin purposes
 */
async function getUserDataForAdmin(username: string) {
  const kv = await Deno.openKv();
  
  try {
    // Get user data
    const userResult = await kv.get(["user", username]);
    const userData = userResult.value as any;
    
    // Get user statistics
    const statsResult = await kv.get(["user_statistics", username]);
    const userStats = statsResult.value as any;
    
    // Get daily game data
    const dailyResult = await kv.get(["daily_games", username]);
    const dailyData = dailyResult.value;
    
    // Count win records
    let winRecordCount = 0;
    for await (const { value } of kv.list({ prefix: ["win_record"] })) {
      const record = value as any;
      if (record.username === username) {
        winRecordCount++;
      }
    }
    
    // Count active sessions
    let sessionCount = 0;
    for await (const { value } of kv.list({ prefix: ["session"] })) {
      const gameState = value as any;
      if (gameState.username === username) {
        sessionCount++;
      }
    }
    
    return {
      userData,
      userStats,
      dailyData: !!dailyData,
      winRecordCount,
      sessionCount,
      exists: !!userData
    };
  } finally {
    kv.close();
  }
}

/**
 * Remove user and all associated data
 */
async function removeUserData(username: string) {
  const kv = await Deno.openKv();
  
  try {
    let deletionCount = 0;
    const deletedItems: string[] = [];
    
    // Remove user profile
    const userResult = await kv.get(["user", username]);
    if (userResult.value) {
      await kv.delete(["user", username]);
      deletionCount++;
      deletedItems.push("user_profile");
    }
    
    // Remove user statistics
    const statsResult = await kv.get(["user_statistics", username]);
    if (statsResult.value) {
      await kv.delete(["user_statistics", username]);
      deletionCount++;
      deletedItems.push("user_statistics");
    }
    
    // Remove daily game data
    const dailyResult = await kv.get(["daily_games", username]);
    if (dailyResult.value) {
      await kv.delete(["daily_games", username]);
      deletionCount++;
      deletedItems.push("daily_games");
    }
    
    // Remove win records
    let winRecordsDeleted = 0;
    for await (const { key, value } of kv.list({ prefix: ["win_record"] })) {
      const record = value as any;
      if (record.username === username) {
        await kv.delete(key);
        winRecordsDeleted++;
        deletionCount++;
      }
    }
    if (winRecordsDeleted > 0) {
      deletedItems.push(`${winRecordsDeleted}_win_records`);
    }
    
    // Remove active sessions
    let sessionsDeleted = 0;
    for await (const { key, value } of kv.list({ prefix: ["session"] })) {
      const gameState = value as any;
      if (gameState.username === username) {
        await kv.delete(key);
        sessionsDeleted++;
        deletionCount++;
      }
    }
    if (sessionsDeleted > 0) {
      deletedItems.push(`${sessionsDeleted}_active_sessions`);
    }
    
    // Remove magic link tokens
    let magicLinksDeleted = 0;
    for await (const { key, value } of kv.list({ prefix: ["magic_link"] })) {
      const linkData = value as any;
      if (linkData.username === username) {
        await kv.delete(key);
        magicLinksDeleted++;
        deletionCount++;
      }
    }
    if (magicLinksDeleted > 0) {
      deletedItems.push(`${magicLinksDeleted}_magic_links`);
    }
    
    // Remove standings entries
    let standingsDeleted = 0;
    for await (const { key, value } of kv.list({ prefix: ["standings"] })) {
      const standing = value as any;
      if (standing.username === username) {
        await kv.delete(key);
        standingsDeleted++;
        deletionCount++;
      }
    }
    if (standingsDeleted > 0) {
      deletedItems.push(`${standingsDeleted}_standings`);
    }
    
    // Remove hangman daily games
    const hangmanDailyResult = await kv.get(["hangman_daily_games", username]);
    if (hangmanDailyResult.value) {
      await kv.delete(["hangman_daily_games", username]);
      deletionCount++;
      deletedItems.push("hangman_daily_games");
    }
    
    // Remove hangman word history
    let wordHistoryDeleted = 0;
    for await (const { key, value } of kv.list({ prefix: ["hangman_word_history"] })) {
      const history = value as any;
      if (history.username === username || (Array.isArray(history) && history.some((entry: any) => entry.username === username))) {
        await kv.delete(key);
        wordHistoryDeleted++;
        deletionCount++;
      }
    }
    if (wordHistoryDeleted > 0) {
      deletedItems.push(`${wordHistoryDeleted}_word_history`);
    }
    
    return {
      success: true,
      deletionCount,
      deletedItems
    };
  } finally {
    kv.close();
  }
}

/**
 * List all users (admin only)
 */
export const adminListUsersHandler = async (request: Request): Promise<Response> => {
  if (!authenticateAdmin(request)) {
    return createErrorResponse("Unauthorized", { 
      context: "Admin API",
      format: "json"
    });
  }

  try {
    const kv = await Deno.openKv();
    const users: Array<{ username: string; displayName: string; createdAt: string }> = [];
    
    for await (const { key, value } of kv.list({ prefix: ["user"] })) {
      if (key.length === 2 && typeof key[1] === "string") {
        const userData = value as any;
        users.push({
          username: userData.username,
          displayName: userData.displayName,
          createdAt: new Date(userData.createdAt).toISOString()
        });
      }
    }
    
    kv.close();
    
    return createJsonResponse({
      success: true,
      users,
      count: users.length
    });
  } catch (error) {
    Logger.error(LogContext.API, "Admin list users failed", error as Error);
    return createErrorResponse("Failed to list users", { 
      context: "Admin API",
      format: "json"
    });
  }
};

/**
 * Get user details (admin only)
 */
export const adminGetUserHandler = async (request: Request, params: Record<string, string>): Promise<Response> => {
  if (!authenticateAdmin(request)) {
    return createErrorResponse("Unauthorized", { 
      context: "Admin API",
      format: "json"
    });
  }

  const username = params.username;
  if (!username) {
    return createErrorResponse("Username parameter required", { 
      context: "Admin API",
      format: "json"
    });
  }

  try {
    const userData = await getUserDataForAdmin(username);
    
    if (!userData.exists) {
      return createErrorResponse("User not found", { 
        context: "Admin API",
        format: "json"
      });
    }
    
    return createJsonResponse({
      success: true,
      user: {
        username: userData.userData.username,
        displayName: userData.userData.displayName,
        createdAt: new Date(userData.userData.createdAt).toISOString(),
        statistics: userData.userStats,
        hasDailyData: userData.dailyData,
        winRecordCount: userData.winRecordCount,
        activeSessionCount: userData.sessionCount
      }
    });
  } catch (error) {
    Logger.error(LogContext.API, "Admin get user failed", error as Error);
    return createErrorResponse("Failed to get user data", { 
      context: "Admin API",
      format: "json"
    });
  }
};

/**
 * Remove user (admin only)
 */
export const adminRemoveUserHandler = async (request: Request, params: Record<string, string>): Promise<Response> => {
  if (!authenticateAdmin(request)) {
    return createErrorResponse("Unauthorized", { 
      context: "Admin API",
      format: "json"
    });
  }

  const username = params.username;
  if (!username) {
    return createErrorResponse("Username parameter required", { 
      context: "Admin API",
      format: "json"
    });
  }

  // Check for dry-run parameter
  const url = new URL(request.url);
  const dryRun = url.searchParams.get("dry-run") === "true";

  try {
    // Check if user exists first (but don't fail if not found in main store)
    const userData = await getUserDataForAdmin(username);

    // If dry-run, return what would be deleted without actually deleting
    if (dryRun) {
      const kv = await Deno.openKv();
      
      try {
        const previewItems = [];
        
        // Check ALL KV stores independently (don't rely on getUserDataForAdmin)
        
        // Check user profile
        const userResult = await kv.get(["user", username]);
        if (userResult.value) previewItems.push("user_profile");
        
        // Check user statistics
        const statsResult = await kv.get(["user_statistics", username]);
        if (statsResult.value) previewItems.push("user_statistics");
        
        // Check daily games
        const dailyResult = await kv.get(["daily_games", username]);
        if (dailyResult.value) previewItems.push("daily_games");
        
        // Check hangman daily games
        const hangmanDaily = await kv.get(["hangman_daily_games", username]);
        if (hangmanDaily.value) previewItems.push("hangman_daily_games");
        
        // Count records in list-based stores
        let winRecords = 0, sessions = 0, magicLinks = 0, standings = 0, wordHistory = 0;
        
        // Check win records
        for await (const { value } of kv.list({ prefix: ["win_record"] })) {
          const record = value as any;
          if (record && record.username === username) winRecords++;
        }
        
        // Check active sessions
        for await (const { value } of kv.list({ prefix: ["session"] })) {
          const gameState = value as any;
          if (gameState && gameState.username === username) sessions++;
        }
        
        // Check magic links
        for await (const { value } of kv.list({ prefix: ["magic_link"] })) {
          const linkData = value as any;
          if (linkData && linkData.username === username) magicLinks++;
        }
        
        // Check standings
        for await (const { value } of kv.list({ prefix: ["standings"] })) {
          const standing = value as any;
          if (standing && standing.username === username) standings++;
        }
        
        // Check word history
        for await (const { value } of kv.list({ prefix: ["hangman_word_history"] })) {
          const history = value as any;
          if (history && (history.username === username || (Array.isArray(history) && history.some((entry: any) => entry && entry.username === username)))) {
            wordHistory++;
          }
        }
        
        // Add counts to preview
        if (winRecords > 0) previewItems.push(`${winRecords}_win_records`);
        if (sessions > 0) previewItems.push(`${sessions}_active_sessions`);
        if (magicLinks > 0) previewItems.push(`${magicLinks}_magic_links`);
        if (standings > 0) previewItems.push(`${standings}_standings`);
        if (wordHistory > 0) previewItems.push(`${wordHistory}_word_history`);
        
        return createJsonResponse({
          success: true,
          dryRun: true,
          message: `DRY RUN: Would remove user ${username}`,
          estimatedDeletionCount: previewItems.length,
          itemsToDelete: previewItems,
          scanDetails: {
            userProfile: !!userResult.value,
            userStats: !!statsResult.value,
            dailyGames: !!dailyResult.value,
            hangmanDailyGames: !!hangmanDaily.value,
            winRecords,
            sessions,
            magicLinks,
            standings,
            wordHistory
          }
        });
      } finally {
        kv.close();
      }
    }
    
    // Log the removal attempt
    Logger.warn(LogContext.API, "Admin user removal initiated", {
      targetUser: username,
      foundInMainStore: userData.exists,
      adminRequest: {
        userAgent: request.headers.get("user-agent"),
        ip: request.headers.get("cf-connecting-ip") || request.headers.get("x-forwarded-for")
      }
    });
    
    // Remove the user from ALL KV stores (even if not found in main user store)
    const result = await removeUserData(username);
    
    Logger.warn(LogContext.API, "User removal completed by admin", {
      targetUser: username,
      foundInMainStore: userData.exists,
      deletionCount: result.deletionCount,
      deletedItems: result.deletedItems
    });
    
    const message = userData.exists 
      ? `User ${username} has been permanently removed from all systems`
      : `Cleaned up any remaining data for ${username} (user not found in main store but may have existed in other systems)`;
    
    return createJsonResponse({
      success: true,
      message,
      foundInMainStore: userData.exists,
      deletionCount: result.deletionCount,
      deletedItems: result.deletedItems
    });
  } catch (error) {
    Logger.error(LogContext.API, "Admin remove user failed", error as Error);
    return createErrorResponse("Failed to remove user", { 
      context: "Admin API",
      format: "json"
    });
  }
};