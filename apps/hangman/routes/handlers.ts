import { createGame, processGuess, getHint } from "../state/game.ts";
import { createSession } from "../state/session.ts";
import { homePage } from "../views/home.ts";
import { GameState } from "../types.ts";
import { SimpleAuthState } from "../../../libs/auth/index.ts";
import { Result, ok, err } from "../../../libs/utils/result.ts";
import { getGameSession, updateGameSession, validateSession } from "../utils/session.ts";
import { createHtmlResponse, createHtmlResponseWithSession, createJsonResponse, createStaticResponse } from "../../../libs/utils/http.ts";
import { createErrorResponse, Errors } from "../../../libs/utils/errors.ts";
import { handleTimeExpiredStatistics } from "../utils/statistics.ts";
import { Lo<PERSON>, LogContext } from "../../../libs/utils/logger.ts";
import { GAME_CONFIG, MESSAGES } from "../constants.ts";
import { HTTP_STATUS, REGEX_PATTERNS } from "../../../libs/core/constants.ts";
import { checkDailyLimit, getTimeUntilReset } from "../utils/dailyGameLimit.ts";
import { 
  createGameResponse, 
  checkTimeExpiration, 
  handleGameCompletionIfNeeded, 
  handleCommonErrors,
  withSessionValidation,
  type SessionValidationResult
} from "../utils/handlerUtils.ts";
import { validateGameAccess } from "../utils/authHelpers.ts";
import { resolveStaticFilePath, readFileContents } from "../utils/fileHelpers.ts";

const createNewGameSession = async (authState?: SimpleAuthState): Promise<Result<[string, GameState], Error>> => {
  // Check daily limit before creating new game
  if (authState?.username) {
    const accessResult = await validateGameAccess(authState.username);
    if (!accessResult.ok) {
      return err(accessResult.error);
    }
  }

  const gameResult = await createGame(
    GAME_CONFIG.DEFAULT_DIFFICULTY, 
    GAME_CONFIG.DEFAULT_CATEGORY, 
    GAME_CONFIG.DEFAULT_WORD_COUNT, 
    authState?.username
  );
  if (!gameResult.ok) {
    return gameResult;
  }

  const gameState = gameResult.value;
  const sessionId = createSession(gameState);

  return ok([sessionId, gameState]);
};

export const gameHandler = async (request: Request, _params?: Record<string, string>, authState?: SimpleAuthState): Promise<Response> => {
  const [sessionId, gameState] = getGameSession(request);

  // Validate session and get properly typed values
  const validSession = validateSession(sessionId, gameState);
  if (!validSession) {
    const { welcomeScreen, homePage } = await import("../views/home.ts");
    const content = welcomeScreen(authState?.username);
    
    return createHtmlResponse(homePage(content));
  }

  // Now we have properly typed sessionId and gameState
  return await createGameResponse(request, validSession.gameState, validSession.sessionId);
};

/**
 * Handle new game creation request
 */
export const newGameHandler = async (request: Request, authState?: SimpleAuthState): Promise<Response> => {
  const sessionResult = await createNewGameSession(authState);

  if (!sessionResult.ok) {
    // Use consolidated error handling
    const errorResponse = await handleCommonErrors(request, sessionResult.error, authState);
    if (errorResponse) {
      return errorResponse;
    }
    
    return createErrorResponse(Errors.internal(sessionResult.error.message), { format: "html" });
  }

  const [sessionId, gameState] = sessionResult.value;

  return await createGameResponse(request, gameState, sessionId);
};

/**
 * Handle letter guess request
 */
export const guessHandler = withSessionValidation(async (request: Request, session: SessionValidationResult, params: Record<string, string>): Promise<Response> => {
  const letter = params.letter?.toUpperCase() || "";
  if (!REGEX_PATTERNS.LETTER.test(letter)) {
    return createErrorResponse(Errors.validation(MESSAGES.INVALID_LETTER), { format: "html" });
  }

  // Check time expiration
  const timeCheck = await checkTimeExpiration(session.gameState, session.sessionId);
  if (!timeCheck.ok) {
    return timeCheck.response;
  }

  const currentState = timeCheck.value;
  if (currentState.status !== "playing") {
    return await createGameResponse(request, currentState, session.sessionId);
  }

  // Process the guess and update the session
  const updatedStateResult = processGuess(currentState, letter);

  if (!updatedStateResult.ok) {
    return createErrorResponse(Errors.game(updatedStateResult.error.message), { format: "html" });
  }

  const updatedState = updatedStateResult.value;

  // Handle game completion statistics
  await handleGameCompletionIfNeeded(currentState, updatedState, "guess");

  updateGameSession(session.sessionId, updatedState);

  return await createGameResponse(request, updatedState, session.sessionId);
});

/**
 * Handle hint request
 */
export const hintHandler = withSessionValidation(async (request: Request, session: SessionValidationResult): Promise<Response> => {
  // Check time expiration
  const timeCheck = await checkTimeExpiration(session.gameState, session.sessionId);
  if (!timeCheck.ok) {
    return timeCheck.response;
  }

  const currentState = timeCheck.value;
  if (currentState.status !== "playing") {
    return await createGameResponse(request, currentState, session.sessionId);
  }

  // Process the hint and update the session
  const updatedStateResult = getHint(currentState);

  if (!updatedStateResult.ok) {
    return createErrorResponse(Errors.game(updatedStateResult.error.message), { format: "html" });
  }

  const updatedState = updatedStateResult.value;

  // Handle game completion statistics
  await handleGameCompletionIfNeeded(currentState, updatedState, "hint");

  updateGameSession(session.sessionId, updatedState);

  return await createGameResponse(request, updatedState, session.sessionId);
});

/**
 * Handle time expired request
 */
export const timeExpiredHandler = withSessionValidation(async (request: Request, session: SessionValidationResult): Promise<Response> => {
  // Check if game is still playing and time has actually expired
  if (session.gameState.status !== "playing") {
    // Game already ended, just return current state
    return await createGameResponse(request, session.gameState, session.sessionId);
  }

  const { checkTimeExpired, createTimeExpiredState } = await import("../state/game.ts");
  
  if (!checkTimeExpired(session.gameState)) {
    // Time hasn't actually expired yet, return current state
    return await createGameResponse(request, session.gameState, session.sessionId);
  }

  // Create time expired state
  const updatedState = createTimeExpiredState(session.gameState);

  // Handle time expired statistics
  await handleTimeExpiredStatistics(updatedState);

  updateGameSession(session.sessionId, updatedState);

  return await createGameResponse(request, updatedState, session.sessionId);
});



/**
 * Handle standings API request
 */
export const standingsApiHandler = async (_request: Request, authState?: SimpleAuthState): Promise<Response> => {

  try {
    const { getPlayerStandings } = await import("../../../libs/auth/kv.ts");
    const { playerStandingsContent } = await import("../views/home.ts");
    
    const standings = await getPlayerStandings(20); // Top 20 players
    const currentUser = authState?.username;
    
    const standingsContent = playerStandingsContent(standings, currentUser);
    
    // Wrap the content in a container suitable for the main content area
    const content = `
      <div class="content-wrapper standings-wrapper">
        <div class="content-header">
          <h2>🏆 Player Standings</h2>
        </div>
        ${standingsContent}
      </div>
    `;
    
    return createHtmlResponse(content);
  } catch (error) {
    Logger.error(LogContext.API, 'Failed to get player standings', error as Error);
    return createHtmlResponse('<div class="error">Failed to load standings</div>');
  }
};

/**
 * Handle user stats API request
 */
export const userStatsApiHandler = async (_request: Request, authState?: SimpleAuthState): Promise<Response> => {
  if (!authState?.username) {
    return createHtmlResponse('<div class="error">Not authenticated</div>');
  }


  try {
    const { getUserStatistics } = await import("../../../libs/auth/kv.ts");
    const { gameStatsContent } = await import("../views/home.ts");
    
    const statistics = await getUserStatistics(authState.username);
    
    // Create a minimal game state object for the stats display
    const mockGameState: GameState = {
      id: "stats-view",
      word: "",
      guessedLetters: new Set(),
      wrongGuesses: 0,
      maxWrong: 7,
      status: "playing" as const,
      difficulty: "medium" as const,
      category: "general",
      hintsUsed: 0,
      hintsAllowed: 3,
      startTime: 0,
      endTime: 0,
      timeLimit: 60,
      statistics,
      username: authState.username
    };
    
    const statsContent = gameStatsContent(mockGameState);
    
    // Wrap the content in a container suitable for the main content area
    const content = `
      <div class="content-wrapper">
        <div class="content-header">
          <h2>📊 Your Statistics</h2>
        </div>
        ${statsContent}
      </div>
    `;
    
    return createHtmlResponse(content);
  } catch (error) {
    Logger.error(LogContext.API, 'Failed to get user statistics', error as Error);
    return createHtmlResponse('<div class="error">Failed to load statistics</div>');
  }
};

/**
 * Handle static file requests
 */
export const staticFileHandler = async (request: Request): Promise<Response> => {
  const url = new URL(request.url);
  const filePath = url.pathname.replace(/^\/static\//, "");

  // Resolve file path using centralized utility
  const pathResult = await resolveStaticFilePath(filePath);
  if (!pathResult.ok) {
    Logger.error(LogContext.STATIC, `Failed to locate file: ${filePath}`, pathResult.error);
    return createErrorResponse(Errors.notFound(`File not found: ${filePath}`), { format: "html" });
  }

  // Read file contents using centralized utility
  const contentsResult = await readFileContents(filePath, pathResult.value);
  if (!contentsResult.ok) {
    Logger.error(LogContext.STATIC, `Failed to read file: ${filePath}`, contentsResult.error);
    return createErrorResponse(Errors.internal(`Failed to read file: ${filePath}`), { format: "html" });
  }

  return createStaticResponse(contentsResult.value, filePath);
};

export const dailyLimitInfoHandler = async (request: Request, _params?: Record<string, string>, authState?: SimpleAuthState): Promise<Response> => {
  if (!authState?.isAuthenticated || !authState.username) {
    return createJsonResponse({ error: "Authentication required" }, 401);
  }

  try {
    const limitInfo = await checkDailyLimit(authState.username);
    const resetInfo = getTimeUntilReset();
    
    return createJsonResponse({
      ...limitInfo,
      resetTime: resetInfo
    });
  } catch (error) {
    Logger.error(LogContext.GAME, "Failed to get daily limit info", error as Error);
    return createJsonResponse({ error: "Failed to get daily limit info" }, 500);
  }
};

