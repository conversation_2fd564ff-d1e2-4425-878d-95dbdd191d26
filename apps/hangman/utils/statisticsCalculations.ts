import { GameStatistics } from "../types.ts";

/**
 * Centralized statistics calculations to reduce duplication
 * Follows functional programming principles with pure functions
 */

/**
 * Calculate statistics for a winning game
 */
export const calculateWinStatistics = (
  currentStats: GameStatistics, 
  totalGuesses: number
): GameStatistics => ({
  gamesPlayed: currentStats.gamesPlayed + 1,
  gamesWon: currentStats.gamesWon + 1,
  currentStreak: currentStats.currentStreak + 1,
  bestStreak: Math.max(currentStats.bestStreak, currentStats.currentStreak + 1),
  totalGuesses: currentStats.totalGuesses + totalGuesses,
  averageGuessesPerWin: Math.round((currentStats.totalGuesses + totalGuesses) / (currentStats.gamesWon + 1))
});

/**
 * Calculate statistics for a losing game (includes time expiration)
 */
export const calculateLossStatistics = (
  currentStats: GameStatistics, 
  totalGuesses: number
): GameStatistics => ({
  gamesPlayed: currentStats.gamesPlayed + 1,
  gamesWon: currentStats.gamesWon,
  currentStreak: 0, // Reset streak on loss
  bestStreak: currentStats.bestStreak,
  totalGuesses: currentStats.totalGuesses + totalGuesses,
  averageGuessesPerWin: currentStats.gamesWon > 0 ? 
    Math.round((currentStats.totalGuesses + totalGuesses) / currentStats.gamesWon) : 0
});

/**
 * Calculate game duration in seconds from timestamps
 */
export const calculateGameDuration = (startTime: number, endTime?: number): number => {
  const actualEndTime = endTime || Date.now();
  const durationMs = actualEndTime - startTime;
  return Math.round(durationMs / 1000);
};

/**
 * Prepare win record data for storage
 */
export const createWinRecord = (
  sequenceNumber: number,
  username: string,
  gameState: {
    word: string;
    id: string;
    startTime: number;
    endTime?: number | null;
    guessedLetters: ReadonlySet<string>;
    hintsUsed: number;
    difficulty: string;
    category: string;
  },
  completionMethod: "guess" | "hint"
) => {
  const endTime = gameState.endTime || Date.now();
  const duration = calculateGameDuration(gameState.startTime, endTime);
  
  return {
    sequenceNumber,
    username,
    word: gameState.word,
    completionTime: new Date(endTime).toISOString(),
    duration,
    totalGuesses: gameState.guessedLetters.size,
    hintsUsed: gameState.hintsUsed,
    completionMethod,
    difficulty: gameState.difficulty,
    category: gameState.category,
    gameId: gameState.id
  };
};