import { SimpleAuthState } from "../../../libs/auth/index.ts";
import { GameState, SessionValidationResult } from "../types.ts";
import { HandlerResult } from "../../../libs/types/shared.ts";
import { validateSessionResult, getGameSession } from "./session.ts";
import { Result, ok, err, map } from "../../../libs/utils/result.ts";
import { createHtmlResponse, createHtmlResponseWithSession, isHtmxRequest } from "../../../libs/utils/http.ts";
import { ResponseFactory } from "../../../libs/utils/responses.ts";
import { homePage, gameComponent, gameContentOnly } from "../views/home.ts";
import { Logger, LogContext } from "../../../libs/utils/logger.ts";
import { MESSAGES } from "../constants.ts";
import { createErrorResponse, Errors } from "../../../libs/utils/errors.ts";

/**
 * Consolidated handler utilities to reduce duplication in route handlers
 */

// Re-export shared types for backward compatibility
export type { HandlerResult } from "../../../libs/types/shared.ts";
export type { SessionValidationResult } from "../types.ts";

/**
 * Validate game session using enhanced Result types
 */
export const validateGameSessionResult = (request: Request): Result<SessionValidationResult, string> => {
  const sessionResult = validateSessionResult(request);
  
  return map(sessionResult, ({ sessionId, gameState }) => ({
    sessionId,
    gameState
  }));
};

/**
 * Backward compatibility wrapper - maintains existing signature
 */
export const validateGameSession = (request: Request): HandlerResult<SessionValidationResult> => {
  const result = validateGameSessionResult(request);
  
  if (result.ok) {
    return {
      ok: true,
      value: result.value
    };
  }
  
  return {
    ok: false,
    response: createErrorResponse(Errors.session(MESSAGES.NO_ACTIVE_GAME), { format: "html" })
  };
};

/**
 * Create appropriate response based on whether request is HTMX or full page
 * Enhanced version using the unified ResponseFactory
 */
export const createAdaptiveResponse = (
  request: Request,
  content: string,
  sessionId?: string
): Response => {
  return ResponseFactory.htmx.adaptive(
    request,
    content,
    (content) => homePage(content), // Full page wrapper
    { sessionId } // Options including session
  );
};

/**
 * Create game component response with proper wrapping for HTMX
 * Enhanced version using the unified ResponseFactory
 */
export const createGameResponse = async (
  request: Request,
  gameState: GameState,
  sessionId: string
): Promise<Response> => {
  // Get games remaining count for authenticated users
  let gamesRemaining: number | undefined;
  if (gameState.username) {
    try {
      const { checkDailyLimit } = await import("../utils/dailyGameLimit.ts");
      const limitCheck = await checkDailyLimit(gameState.username);
      gamesRemaining = limitCheck.gamesRemaining;
    } catch (error) {
      Logger.warn(LogContext.GAME, "Failed to get daily limit for game response", { 
        username: gameState.username, 
        error: error instanceof Error ? error.message : String(error) 
      });
    }
  }

  const content = ResponseFactory.htmx.isRequest(request) 
    ? gameContentOnly(gameState, gamesRemaining)  // HTMX request - just game content
    : gameComponent(gameState, gamesRemaining);   // Full page request - full component

  return ResponseFactory.htmx.adaptive(
    request,
    content,
    (content) => homePage(content), // Full page wrapper for non-HTMX requests
    { sessionId }
  );
};

/**
 * Handle time expiration check with consistent logging
 */
export const checkTimeExpiration = async (
  gameState: GameState,
  sessionId: string
): Promise<HandlerResult<GameState>> => {
  const { checkTimeExpired, createTimeExpiredState } = await import("../state/game.ts");
  const { handleTimeExpiredStatistics } = await import("./statistics.ts");
  const { updateGameSession } = await import("./session.ts");
  
  if (checkTimeExpired(gameState)) {
    Logger.log(LogContext.GAME, "Time expired for game", { gameId: gameState.id });
    
    const timeExpiredState = createTimeExpiredState(gameState);
    
    // Handle time expired statistics
    await handleTimeExpiredStatistics(timeExpiredState);
    
    // Update session
    updateGameSession(sessionId, timeExpiredState);
    
    return {
      ok: true,
      value: timeExpiredState
    };
  }
  
  return {
    ok: true,
    value: gameState
  };
};

/**
 * Handle game completion with consistent logging and statistics
 */
export const handleGameCompletionIfNeeded = async (
  oldState: GameState,
  newState: GameState,
  completionType: "guess" | "hint" | "timeout"
): Promise<void> => {
  const { handleGameCompletion } = await import("./statistics.ts");
  
  if (oldState.status === "playing" && (newState.status === "won" || newState.status === "lost")) {
    Logger.log(LogContext.GAME, "Game completed", { 
      gameId: newState.id,
      oldStatus: oldState.status,
      newStatus: newState.status,
      completionType
    });
    
    // Map timeout to hint for the statistics function
    const statisticsCompletionType: "guess" | "hint" = completionType === "timeout" ? "hint" : completionType;
    await handleGameCompletion(newState, statisticsCompletionType);
  }
};

// Re-export parseErrorMessage from centralized error utilities
// Kept for backwards compatibility - uses centralized implementation
export { parseErrorMessage } from "../../../libs/utils/errors.ts";

/**
 * Handle common error types with appropriate responses
 */
export const handleCommonErrors = async (
  request: Request,
  error: Error,
  authState?: SimpleAuthState
): Promise<Response | null> => {
  const errorInfo = parseErrorMessage(error);
  
  switch (errorInfo.type) {
    case 'DailyLimitError': {
      const { dailyLimitReached } = await import("../views/home.ts");
      const content = dailyLimitReached(
        errorInfo.data.gamesPlayed,
        errorInfo.data.gamesRemaining,
        authState?.username
      );
      
      if (isHtmxRequest(request)) {
        return createHtmlResponse(content);
      } else {
        const { dailyLimitReachedPage } = await import("../views/home.ts");
        return createHtmlResponse(dailyLimitReachedPage(
          errorInfo.data.gamesPlayed,
          errorInfo.data.gamesRemaining,
          authState?.username
        ));
      }
    }
    
    default:
      return null; // Let the caller handle generic errors
  }
};

// Re-export withErrorHandling from centralized error utilities
// Provides enhanced error handling with automatic response generation
export { withErrorHandling } from "../../../libs/utils/errors.ts";

/**
 * Create a higher-order function for session validation
 */
export const withSessionValidation = <T extends any[], R>(
  handler: (request: Request, session: SessionValidationResult, ...args: T) => Promise<R>
) => {
  return async (request: Request, ...args: T): Promise<R | Response> => {
    const sessionResult = validateGameSession(request);
    
    if (!sessionResult.ok) {
      return sessionResult.response;
    }
    
    return handler(request, sessionResult.value, ...args);
  };
};
