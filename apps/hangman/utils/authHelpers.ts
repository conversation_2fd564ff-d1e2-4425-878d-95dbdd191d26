import { Result, ok, err } from "../../../libs/utils/result.ts";
import { Logger, LogContext } from "../../../libs/utils/logger.ts";

/**
 * Authentication helper utilities to reduce duplication
 * Follows functional programming principles with Result types
 */


/**
 * Check daily game limit for a user
 */
export const checkDailyGameLimit = async (username: string): Promise<Result<{ gamesPlayed: number; gamesRemaining: number; canPlay: boolean }, Error>> => {
  try {
    const { checkDailyLimit } = await import("../../../libs/auth/kv.ts");
    const limitCheck = await checkDailyLimit(username);
    return ok(limitCheck);
  } catch (error) {
    Logger.error(LogContext.AUTH, 'Failed to check daily limit', error as Error);
    return err(error as Error);
  }
};

/**
 * Validate game access for authenticated users
 * Checks daily limits
 */
export const validateGameAccess = async (username: string): Promise<Result<void, Error>> => {
  
  // Check daily limit
  const limitResult = await checkDailyGameLimit(username);
  if (!limitResult.ok) {
    return err(limitResult.error);
  }
  
  if (!limitResult.value.canPlay) {
    return err(new Error(`DAILY_LIMIT_REACHED:${limitResult.value.gamesPlayed}:${limitResult.value.gamesRemaining}`));
  }
  
  return ok(undefined);
};

