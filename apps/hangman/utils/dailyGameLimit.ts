import { getKv } from "../../../libs/auth/index.ts";
import { Logger, LogContext } from "../../../libs/utils/logger.ts";

// Daily game limit configuration
export const DAILY_GAME_LIMIT = 5;

// Daily game tracking types
export interface UserDailyGames {
  username: string;
  date: string; // YYYY-MM-DD format
  gamesPlayed: number;
  lastGameTime: number; // timestamp
}

/**
 * Get today's date in YYYY-MM-DD format
 */
function getTodayString(): string {
  return new Date().toISOString().split('T')[0];
}

/**
 * Get user's daily game count for today
 */
export async function getDailyGameCount(username: string): Promise<number> {
  const kvStore = await getKv();
  const today = getTodayString();
  const result = await kvStore.get<UserDailyGames>(["hangman_daily_games", username, today]);
  
  return result.value?.gamesPlayed || 0;
}

/**
 * Get user's daily game data for today
 */
export async function getUserDailyGames(username: string): Promise<UserDailyGames> {
  const kvStore = await getKv();
  const today = getTodayString();
  const result = await kvStore.get<UserDailyGames>(["hangman_daily_games", username, today]);
  
  return result.value || {
    username,
    date: today,
    gamesPlayed: 0,
    lastGameTime: 0
  };
}

/**
 * Increment user's daily game count
 */
export async function incrementDailyGameCount(username: string): Promise<UserDailyGames> {
  const kvStore = await getKv();
  const today = getTodayString();
  const currentData = await getUserDailyGames(username);
  
  const updatedData: UserDailyGames = {
    ...currentData,
    gamesPlayed: currentData.gamesPlayed + 1,
    lastGameTime: Date.now()
  };
  
  await kvStore.set(["hangman_daily_games", username, today], updatedData);
  
  Logger.log(LogContext.GAME, "Daily game count incremented", { 
    username, 
    date: today, 
    gamesPlayed: updatedData.gamesPlayed 
  });
  
  return updatedData;
}

/**
 * Check if user has exceeded daily game limit
 */
export async function checkDailyLimit(username: string): Promise<{
  hasExceeded: boolean;
  gamesPlayed: number;
  gamesRemaining: number;
  limit: number;
}> {
  const gamesPlayed = await getDailyGameCount(username);
  const hasExceeded = gamesPlayed >= DAILY_GAME_LIMIT;
  const gamesRemaining = Math.max(0, DAILY_GAME_LIMIT - gamesPlayed);
  
  return {
    hasExceeded,
    gamesPlayed,
    gamesRemaining,
    limit: DAILY_GAME_LIMIT
  };
}

/**
 * Validate that user can start a new game
 */
export async function validateGameAccess(username: string): Promise<{
  canPlay: boolean;
  gamesPlayed: number;
  gamesRemaining: number;
  limit: number;
  message?: string;
}> {
  const limitCheck = await checkDailyLimit(username);
  
  if (limitCheck.hasExceeded) {
    return {
      canPlay: false,
      ...limitCheck,
      message: `Daily limit of ${DAILY_GAME_LIMIT} games reached. Try again tomorrow!`
    };
  }
  
  return {
    canPlay: true,
    ...limitCheck
  };
}

/**
 * Clean up old daily game records (older than 7 days)
 */
export async function cleanupOldDailyRecords(): Promise<void> {
  const kvStore = await getKv();
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
  const cutoffDate = sevenDaysAgo.toISOString().split('T')[0];
  
  let deletedCount = 0;
  
  for await (const { key, value } of kvStore.list<UserDailyGames>({ prefix: ["hangman_daily_games"] })) {
    if (value && value.date < cutoffDate) {
      await kvStore.delete(key);
      deletedCount++;
    }
  }
  
  Logger.log(LogContext.GAME, "Daily records cleanup completed", { deletedCount, cutoffDate });
}

/**
 * Get time until next reset (midnight)
 */
export function getTimeUntilReset(): {
  hours: number;
  minutes: number;
  totalMinutes: number;
} {
  const now = new Date();
  const tomorrow = new Date(now);
  tomorrow.setDate(tomorrow.getDate() + 1);
  tomorrow.setHours(0, 0, 0, 0);
  
  const msUntilReset = tomorrow.getTime() - now.getTime();
  const totalMinutes = Math.floor(msUntilReset / (1000 * 60));
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;
  
  return { hours, minutes, totalMinutes };
}