import { Result, ok, err } from "../../../libs/utils/result.ts";
import { Logger, LogContext } from "../../../libs/utils/logger.ts";

/**
 * File handling utilities to reduce complexity in static file serving
 * Follows functional programming principles with Result types
 */

/**
 * Attempt to resolve a static file path from multiple possible locations
 */
export const resolveStaticFilePath = async (filePath: string): Promise<Result<string, Error>> => {
  const possiblePaths = [
    // Try direct path (works in deployment when using cd to app dir)
    `./static/${filePath}`,
    // Try relative to module
    new URL(`../static/${filePath}`, import.meta.url).pathname,
    // Try src/static path
    `${Deno.cwd()}/src/static/${filePath}`
  ];

  for (const path of possiblePaths) {
    try {
      await Deno.stat(path);
      Logger.debug(LogContext.STATIC, `File found at: ${path}`, { filePath });
      return ok(path);
    } catch {
      // Continue to next path
      continue;
    }
  }

  return err(new Error(`File not found in any location: ${filePath}`));
};

/**
 * Read file contents based on file type
 */
export const readFileContents = async (filePath: string, resolvedPath: string): Promise<Result<string | Uint8Array, Error>> => {
  try {
    const isTextFile = filePath.endsWith(".css") || filePath.endsWith(".js");
    
    if (isTextFile) {
      const contents = await Deno.readTextFile(resolvedPath);
      return ok(contents);
    } else {
      const contents = await Deno.readFile(resolvedPath);
      return ok(contents);
    }
  } catch (error) {
    return err(error as Error);
  }
};

