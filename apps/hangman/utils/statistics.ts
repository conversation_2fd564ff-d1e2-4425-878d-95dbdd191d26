import { GameState } from "../types.ts";
import { logError, logWarning, LogContext } from "../../../libs/utils/logger.ts";
import { 
  calculateWinStatistics, 
  calculateLossStatistics, 
  createWinRecord 
} from "./statisticsCalculations.ts";

/**
 * Hangman game statistics utilities
 */

/**
 * Handle game completion statistics update
 * Consolidates the logic for updating stats when a game is completed
 */
export async function handleGameCompletion(gameState: GameState, completionMethod: "guess" | "hint"): Promise<void> {
  try {
    if (!gameState.username || gameState.status === "playing") {
      return; // No username or game still playing
    }

    const { 
      recordWin, 
      getNextWinSequence, 
      updatePlayerStanding, 
      getUserStatistics, 
      updateUserStatistics,
      incrementDailyGameCount 
    } = await import("../../../libs/auth/kv.ts");
    
    // Record game completion for daily limit tracking
    await incrementDailyGameCount(gameState.username);
    
    // Only record wins for statistics
    if (gameState.status === "won") {
      // Generate win sequence number
      const sequenceNumber = await getNextWinSequence();
      
      // Create win record using centralized function
      const winRecord = createWinRecord(sequenceNumber, gameState.username, gameState, completionMethod);
      
      // Record the win
      await recordWin(winRecord);
      
      // Update player standings
      await updatePlayerStanding(gameState.username, winRecord.duration);
      
      // Update user statistics using centralized calculation
      const currentStats = await getUserStatistics(gameState.username);
      const newStats = calculateWinStatistics(currentStats, gameState.guessedLetters.size);
      
      await updateUserStatistics(gameState.username, newStats);
      
      logWarning(LogContext.STATS, `Win recorded for ${gameState.username}: sequence ${sequenceNumber}`);
    } else if (gameState.status === "lost") {
      // Update statistics for loss using centralized calculation
      const currentStats = await getUserStatistics(gameState.username);
      const newStats = calculateLossStatistics(currentStats, gameState.guessedLetters.size);
      
      await updateUserStatistics(gameState.username, newStats);
      
      logWarning(LogContext.STATS, `Loss recorded for ${gameState.username}`);
    }
    
  } catch (error) {
    logError(LogContext.STATS, error as Error, { 
      operation: 'handle_game_completion',
      username: gameState.username,
      gameId: gameState.id 
    });
  }
}

/**
 * Handle time expired statistics update
 * Consolidates the logic for updating stats when time expires
 */
export async function handleTimeExpiredStatistics(gameState: GameState): Promise<void> {
  try {
    if (!gameState.username) {
      return; // No username to update stats for
    }

    const { 
      getUserStatistics, 
      updateUserStatistics,
      incrementDailyGameCount 
    } = await import("../../../libs/auth/kv.ts");
    
    // Record game completion for daily limit tracking
    await incrementDailyGameCount(gameState.username);
    
    // Update statistics for time expiration (treated as loss)
    const currentStats = await getUserStatistics(gameState.username);
    const newStats = calculateLossStatistics(currentStats, gameState.guessedLetters.size);
    
    await updateUserStatistics(gameState.username, newStats);
    
    logWarning(LogContext.STATS, `Time expired recorded for ${gameState.username}`);
    
  } catch (error) {
    logError(LogContext.STATS, error as Error, { 
      operation: 'handle_time_expired_statistics',
      username: gameState.username,
      gameId: gameState.id 
    });
  }
}