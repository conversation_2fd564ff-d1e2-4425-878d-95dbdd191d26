import { getSession, setSession, createSession, deleteSession } from "../state/session.ts";
import { GameState } from "../types.ts";
import { createGameCookie } from "../../../libs/utils/http.ts";
import { Logger, LogContext } from "../../../libs/utils/logger.ts";
import { SessionCookies, CookieCreators } from "../../../libs/utils/cookies.ts";

/**
 * Hangman-specific session management utilities
 */

/**
 * Extract session ID from request cookies
 * Uses centralized cookie extraction for consistency
 */
export function extractSessionId(request: Request): string | null {
  return SessionCookies.extractGameSession(request);
}

/**
 * Get game session from request
 * Returns tuple of [sessionId, gameState] for consistent handling
 */
export function getGameSession(request: Request): [string | null, GameState | undefined] {
  const sessionId = extractSessionId(request);
  const gameState = sessionId ? getSession(sessionId) : undefined;
  
  return [sessionId, gameState];
}

/**
 * Create a new game session and return the session ID
 * Handles the common pattern of creating session and storing game state
 */
export function createNewSession(gameState: GameState): string {
  return createSession(gameState);
}

/**
 * Update an existing game session
 * Handles the common pattern of updating session with new game state
 */
export function updateGameSession(sessionId: string, gameState: GameState): void {
  setSession(sessionId, gameState);
}

/**
 * Create session response headers
 * Standardizes session cookie creation for responses
 */
export function createSessionHeaders(sessionId: string): Record<string, string> {
  return {
    "Set-Cookie": createGameCookie(sessionId)
  };
}

/**
 * Session validation helper with type guard
 * Checks if session exists and game state is valid
 */
export function isValidSession(sessionId: string | null, gameState: GameState | undefined): sessionId is string {
  return !!(sessionId && gameState);
}

/**
 * Type guard that validates both sessionId and gameState
 */
export function validateSession(sessionId: string | null, gameState: GameState | undefined): { sessionId: string; gameState: GameState } | null {
  if (sessionId && gameState) {
    return { sessionId, gameState };
  }
  return null;
}


/**
 * Extract auth session ID from request cookies
 * Uses centralized cookie extraction for consistency
 */
export function extractAuthSessionId(request: Request): string | null {
  return SessionCookies.extractAuthSession(request);
}

/**
 * Clean up game session on logout
 * Removes the hangman game session to prevent state persistence across logins
 */
export function clearGameSession(request: Request): string {
  const sessionId = extractSessionId(request);
  
  if (sessionId) {
    const deleted = deleteSession(sessionId);
    Logger.log(LogContext.GAME, "Game session cleanup on logout", { sessionId, deleted });
  }
  
  // Return cookie header to clear the hangman_session cookie
  return CookieCreators.clearGameSession();
}

/**
 * Session state helpers for common patterns
 */
export const SessionState = {
  /**
   * Check if user has an active game session
   */
  hasActiveGame(request: Request): boolean {
    const [sessionId, gameState] = getGameSession(request);
    return isValidSession(sessionId, gameState) && gameState?.status === "playing";
  },

  /**
   * Check if user needs to start a new game
   */
  needsNewGame(request: Request): boolean {
    const [sessionId, gameState] = getGameSession(request);
    return !isValidSession(sessionId, gameState) || gameState?.status !== "playing";
  },

  /**
   * Get game status from session
   */
  getGameStatus(request: Request): string | null {
    const [, gameState] = getGameSession(request);
    return gameState?.status || null;
  }
} as const;