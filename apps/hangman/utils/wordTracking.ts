import { getKv } from "../../../libs/auth/index.ts";

// Word tracking types
export interface UserWordHistory {
  username: string;
  usedWords: Record<string, Set<string>>; // category -> Set of used words
  lastReset: Record<string, number>; // category -> timestamp of last reset
}

// Word tracking functions
export async function getUserWordHistory(username: string): Promise<UserWordHistory> {
  const kvStore = await getKv();
  const result = await kvStore.get<UserWordHistory>(["hangman_word_history", username]);
  
  if (!result.value) {
    return {
      username,
      usedWords: {},
      lastReset: {}
    };
  }

  // Convert stored arrays back to Sets
  const usedWords: Record<string, Set<string>> = {};
  for (const [category, words] of Object.entries(result.value.usedWords)) {
    usedWords[category] = new Set(Array.isArray(words) ? words : []);
  }

  return {
    ...result.value,
    usedWords
  };
}

export async function saveUserWordHistory(wordHistory: UserWordHistory): Promise<void> {
  const kvStore = await getKv();
  
  // Convert Sets to arrays for storage
  const usedWords: Record<string, string[]> = {};
  for (const [category, words] of Object.entries(wordHistory.usedWords)) {
    usedWords[category] = Array.from(words);
  }
  
  const storableHistory = {
    ...wordHistory,
    usedWords
  };
  
  await kvStore.set(["hangman_word_history", wordHistory.username], storableHistory);
}

export async function addUsedWord(username: string, category: string, word: string): Promise<void> {
  const wordHistory = await getUserWordHistory(username);
  
  if (!wordHistory.usedWords[category]) {
    wordHistory.usedWords[category] = new Set();
  }
  
  wordHistory.usedWords[category].add(word.toUpperCase());
  await saveUserWordHistory(wordHistory);
}

export async function resetUserWordHistory(username: string, category: string): Promise<void> {
  const wordHistory = await getUserWordHistory(username);
  
  wordHistory.usedWords[category] = new Set();
  wordHistory.lastReset[category] = Date.now();
  
  await saveUserWordHistory(wordHistory);
}

export async function getAvailableWords(username: string, category: string, allWords: readonly string[]): Promise<string[]> {
  const wordHistory = await getUserWordHistory(username);
  const usedWords = wordHistory.usedWords[category] || new Set();
  
  // Filter out already used words
  const availableWords = allWords.filter(word => !usedWords.has(word.toUpperCase()));
  
  // If no words available, reset the history for this category and return all words
  if (availableWords.length === 0) {
    await resetUserWordHistory(username, category);
    return [...allWords];
  }
  
  return availableWords;
}