{"name": "hangman-game", "exports": "./main.ts", "entrypoint": "main.ts", "nodeCompat": true, "include": ["data", "state", "views", "static", "routes", "scripts", "types.ts", "constants.ts", "main.ts"], "tasks": {"dev": "export LOG_LEVEL=DEBUG && deno run --watch --allow-net --allow-read --allow-env --unstable-kv main.ts", "start": "deno run --allow-net --allow-read --allow-env --unstable-kv main.ts", "deploy": "cd ../.. && deployctl deploy --project=hangman --prod --entrypoint=apps/hangman/main.ts --config=apps/hangman/deno.json", "test": "deno check main.ts && cd ../.. && deno check libs/**/*.ts && echo '✅ All type checks passed!'", "test:lint": "deno lint --quiet || echo '⚠️  Found lint issues but project is functional'", "test:startup": "(deno run --allow-net --allow-read --allow-env --unstable-kv main.ts &); sleep 5; pkill -f 'deno run.*main.ts' 2>/dev/null; echo '✅ Server startup test completed'", "code-quality": "npx fta-cli .", "code-quality:json": "npx fta-cli . --json", "code-quality:libs": "cd ../.. && npx fta-cli libs", "kv:clear": "deno run --allow-env --unstable-kv scripts/clear-kv.ts", "kv:clear-force": "deno run --allow-env --unstable-kv scripts/clear-kv.ts --force", "kv:stats": "deno run --allow-env --unstable-kv scripts/kv-utils.ts stats", "kv:list": "deno run --allow-env --unstable-kv scripts/kv-utils.ts list", "kv:clear-users": "deno run --allow-env --unstable-kv scripts/kv-utils.ts clear-users", "kv:clear-games": "deno run --allow-env --unstable-kv scripts/kv-utils.ts clear-games", "kv:clear-sessions": "deno run --allow-env --unstable-kv scripts/kv-utils.ts clear-sessions", "user:list": "deno run --allow-env --unstable-kv scripts/remove-user.ts --list-users", "user:details": "deno run --allow-env --unstable-kv scripts/remove-user.ts --details", "user:remove": "deno run --allow-env --unstable-kv scripts/remove-user.ts", "user:remove-dry": "deno run --allow-env --unstable-kv scripts/remove-user.ts --dry-run", "kv:clear:prod": "bash -c 'source ../../libs/auth/.env && curl -X POST \"https://hangman.deno.dev/admin/kv/clear?type=all&confirm=true\" -H \"Authorization: Bearer $ADMIN_KEY\"'", "user:list:prod": "bash -c 'source ../../libs/auth/.env && curl -X GET \"https://hangman.deno.dev/admin/users\" -H \"Authorization: Bearer $ADMIN_KEY\"'", "user:details:prod": "bash -c 'source ../../libs/auth/.env && curl -X GET \"https://hangman.deno.dev/admin/users/$USER_EMAIL\" -H \"Authorization: Bearer $ADMIN_KEY\"'", "user:remove:dry:prod": "bash -c 'source ../../libs/auth/.env && curl -X DELETE \"https://hangman.deno.dev/admin/users/$USER_EMAIL/remove?dry-run=true\" -H \"Authorization: Bearer $ADMIN_KEY\"'", "user:remove:prod": "bash -c 'source ../../libs/auth/.env && curl -X DELETE \"https://hangman.deno.dev/admin/users/$USER_EMAIL/remove\" -H \"Authorization: Bearer $ADMIN_KEY\"'"}, "imports": {"ts-pattern": "jsr:@gabriel/ts-pattern@^5.6.2/", "@std/assert": "jsr:@std/assert@1", "@effection/effection": "jsr:@effection/effection@^3.4.0"}, "deploy": {"project": "5cd54cd6-8a70-4b90-9fc5-729863c41e65", "exclude": ["**/node_modules", "**/.git", "**/*.lock"]}, "unstable": ["kv"]}