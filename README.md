# Multi-Application Workspace

A modern TypeScript/Deno workspace featuring a multi-application architecture with shared libraries. Currently hosts a Hangman game with full authentication, session management, and email verification.

<p align="center">
  <img src="./apps/hangman/static/images/screenshot.png" alt="Hangman Game Screenshot">
</p>

## 🏗️ Architecture

This project implements a "poor man's monorepo" structure that separates shared infrastructure from application-specific code, making it easy to add new applications while reusing common functionality.

### 📁 Project Structure

```text
/
├── libs/                        # 📚 Shared Libraries
│   ├── auth/                   # 🔐 Authentication system
│   │   ├── config.ts           # Auth configuration
│   │   ├── email.ts            # Email verification service
│   │   ├── index.ts            # Main auth export
│   │   ├── kv.ts               # Database operations
│   │   ├── middleware.ts       # Auth middleware
│   │   ├── protectedRoute.ts   # Route protection
│   │   ├── routes.ts           # Auth endpoints
│   │   ├── types.ts            # Auth type definitions
│   │   └── utils.ts            # Auth utilities
│   ├── core/                   # 🔧 Core infrastructure
│   │   ├── constants.ts        # Shared constants
│   │   ├── router.ts           # URL routing system
│   │   └── types.ts            # Shared type definitions
│   ├── middleware/             # 🛡️ Generic middleware
│   │   └── rateLimiting.ts     # Rate limiting
│   └── utils/                  # 🛠️ Shared utilities
│       ├── array.ts            # Array helpers
│       ├── http.ts             # HTTP utilities
│       ├── logger.ts           # Conditional logging system
│       ├── pattern.ts          # Pattern matching
│       └── result.ts           # Result type for error handling
├── apps/                        # 🎮 Applications (each self-contained)
│   └── hangman/                # Hangman Game App
│       ├── main.ts             # App entry point
│       ├── deno.json           # App-specific configuration
│       ├── deno.lock           # App-specific lock file
│       ├── data/               # Game data (word lists)
│       ├── state/              # Game state management
│       ├── views/              # UI templates
│       ├── static/             # Assets (CSS, JS, images)
│       ├── routes/             # Game handlers
│       ├── utils/              # Hangman-specific utilities
│       ├── scripts/            # App-specific scripts
│       │   ├── clear-kv.ts     # Database cleanup
│       │   └── kv-utils.ts     # Database utilities
│       ├── constants.ts        # Hangman-specific constants
│       └── types.ts            # Hangman-specific types
└── README.md                   # This file
```

## 🎮 Applications

### Hangman Game

A modern, accessible implementation of the classic Hangman word guessing game.

**Features:**
- 🎯 Three difficulty levels (Easy, Medium, Hard)
- 🔤 Multiple word categories (General, Animals, Countries)
- 🏆 Game statistics tracking (wins, streaks, time)
- 💡 Smart hint system to reveal letters when stuck
- ⏰ 60-second time challenges
- 📧 Email verification system
- 👤 WebAuthn authentication (passwordless)
- 📱 Responsive design for all device sizes
- ⌨️ Full keyboard navigation support
- 👁️ Screen reader compatibility for accessibility
- 🎨 Visual hangman representation using SVG
- 🔄 Session-based gameplay
- 📊 Player leaderboards
- 🎯 Daily game limits

**Technologies:**
- **Backend**: Deno, TypeScript, Effection (structured concurrency)
- **Frontend**: HTML templates, CSS, HTMX for interactions
- **Auth**: WebAuthn (biometric/security key authentication)
- **Email**: Resend API for verification emails
- **Database**: Deno KV for session and user data
- **Logging**: Environment-aware conditional logging system
- **Deployment**: Deno Deploy ready

## 🚀 Getting Started

### Prerequisites

- [Deno](https://deno.land/) (version 2.4.0 or higher)

### Development

#### Running the Hangman App

```bash
# Navigate to the app directory
cd apps/hangman

# Create .env file for local development (first time only)
cp .env.example .env
# Edit .env and add your actual RESEND_API_KEY

# Run app tasks
deno task dev        # Development server
deno task start      # Production server
deno task deploy     # Deploy to Deno Deploy

# Database management
deno task kv:clear           # Clear all KV data (with confirmation)
deno task kv:clear-force     # Force clear all KV data (no confirmation)
deno task kv:stats           # Show database statistics
deno task kv:list            # List all entries
deno task kv:clear-users     # Clear only user accounts
deno task kv:clear-games     # Clear only game data
deno task kv:clear-sessions  # Clear only active sessions
```

### 🔧 Alternative: Direct Script Usage

```bash
# From hangman app directory, you can also run scripts directly
./scripts/clear-kv.ts
./scripts/kv-utils.ts stats
```

### 📧 Email Configuration

To enable email verification with Resend:

#### 1. **Set up Resend Account**
1. Sign up for a [Resend](https://resend.com) account
2. Verify a domain at [resend.com/domains](https://resend.com/domains) (e.g., `yourdomain.com`)
3. Create an API key with email sending permissions

#### 2. **Configure Environment Variables**
Set these in your Deno Deploy dashboard or `.env` file:

```bash
# Required for email functionality
RESEND_API_KEY=re_your_actual_api_key_here
BASE_URL=https://your-deployment.deno.dev

# Optional: Force production mode
DENO_ENV=production
```

#### 3. **Domain Setup**
The application is configured to send emails from `<EMAIL>`. To use your own domain:

1. Verify your domain in Resend
2. Update `FROM_EMAIL` in `libs/auth/email.ts` to use your verified domain
3. Redeploy the application

**Note**: Without a verified domain, Resend will only send emails to your own registered email address.

## 🗄️ Database Management

### KV Store Administration

The application includes secure HTTP endpoints for managing the Deno KV database in production.

#### Setup Admin Access

1. **Generate a secure admin key:**
   ```bash
   openssl rand -hex 32
   ```

2. **Set environment variable in Deno Deploy:**
   ```bash
   ADMIN_KEY=your_secure_random_key_here
   ```

#### Production Management Commands

**View database statistics:**
```bash
curl -H "Authorization: Bearer your_admin_key" \
     https://your-hangman.dev/admin/kv/stats
```

**Clear all data (⚠️ Destructive):**
```bash
curl -H "Authorization: Bearer your_admin_key" \
     "https://hangman.deno.dev/admin/kv/clear?type=all&confirm=true"
```

**Clear specific data types:**
```bash
# Clear only user accounts and sessions
curl -H "Authorization: Bearer your_admin_key" \
     "https://your-hangman.dev/admin/kv/clear?type=users&confirm=true"

# Clear only game statistics and leaderboards
curl -H "Authorization: Bearer your_admin_key" \
     "https://your-hangman.dev/admin/kv/clear?type=games&confirm=true"

# Clear only active sessions
curl -H "Authorization: Bearer your_admin_key" \
     "https://your-hangman.dev/admin/kv/clear?type=sessions&confirm=true"
```

**Safety check (no confirmation):**
```bash
curl -H "Authorization: Bearer your_admin_key" \
     "https://your-hangman.dev/admin/kv/clear?type=all"
# Returns available options without executing
```

#### Development Management

For local development, use the included scripts:

```bash
# Navigate to app directory
cd apps/hangman

# Show database statistics
deno run --allow-env --unstable-kv scripts/kv-utils.ts stats

# List all entries
deno run --allow-env --unstable-kv scripts/kv-utils.ts list

# Clear specific data types
deno run --allow-env --unstable-kv scripts/kv-utils.ts clear-users
deno run --allow-env --unstable-kv scripts/kv-utils.ts clear-games
deno run --allow-env --unstable-kv scripts/kv-utils.ts clear-sessions

# Clear all data (with confirmation)
deno run --allow-env --unstable-kv scripts/kv-utils.ts clear
```

#### Security Features

- **🔐 Bearer Token Authentication**: All admin endpoints require a valid admin key
- **⚠️ Confirmation Required**: Destructive operations require `confirm=true` parameter
- **🎯 Type-Specific Operations**: Clear only specific data types to prevent accidents
- **📊 Safe Inspection**: Stats endpoint provides read-only database information
- **🔒 Environment Isolation**: Admin key stored as environment variable, not in code

### 🔍 Logging System

The application features a sophisticated conditional logging system:

- **Environment-aware**: Automatically adjusts log verbosity based on environment
- **Production-optimized**: ERROR-level logging by default in production
- **Development-friendly**: DEBUG-level logging in development
- **Structured logging**: Optional JSON formatting for monitoring systems
- **Performance-focused**: Zero overhead when logging is disabled

Configure logging via environment variables (see Environment Variables section below).

## 🏗️ Adding New Applications

The workspace structure makes it easy to add new applications:

1. **Create app directory:**
   ```bash
   mkdir apps/your-new-app
   cd apps/your-new-app
   ```

2. **Create app files:**
   ```bash
   # App-specific main.ts
   touch main.ts
   
   # App-specific configuration
   touch deno.json
   
   # App-specific constants and types
   touch constants.ts types.ts
   ```

3. **Use shared libraries:**
   ```typescript
   // Import unified auth system
   import { requireAuth, withAuth } from "../../libs/auth/index.ts";
   
   // Import shared utilities
   import { createRouter } from "../../libs/core/router.ts";
   import { Result, ok, err } from "../../libs/utils/result.ts";
   
   // Import shared constants
   import { HTTP_STATUS } from "../../libs/core/constants.ts";
   ```

4. **Create app-specific deno.json:**
   ```json
   {
     "name": "your-new-app",
     "exports": "./main.ts",
     "tasks": {
       "dev": "deno run --watch --allow-net main.ts",
       "start": "deno run --allow-net main.ts"
     },
     "imports": {
       // Add any app-specific imports
     }
   }
   ```

## 🔐 Authentication System

The shared authentication system provides:

- **WebAuthn Support**: Passwordless authentication using biometrics or security keys
- **Email Verification**: Post-registration email verification with magic links
- **Session Management**: Secure session handling with automatic cleanup
- **Rate Limiting**: Protection against brute force attacks
- **User Management**: Complete user lifecycle management

All applications can leverage this authentication system out of the box.

## 🎯 How to Play Hangman

1. **Registration**: Sign up with your email address
2. **Verification**: Click the verification link sent to your email
3. **Authentication**: Use WebAuthn (biometric/security key) for passwordless login
4. **Gameplay**:
   - Choose difficulty level and category
   - Guess letters by clicking or using keyboard
   - Use hints when stuck (limited per game)
   - Beat the 60-second timer
   - Track your progress on the leaderboard
5. **Daily Limits**: Play 5 games and see where are you ranked

## 🚀 Deployment

### Deno Deploy

Deploy the hangman application:

```bash
# Navigate to app directory
cd apps/hangman

# Deploy to Deno Deploy
deno task deploy
```

**Note**: The deployment runs from the root directory to ensure all shared libraries (`libs/`) are included.

#### Troubleshooting Deployment

**Common issues and solutions:**

1. **Network errors during deployment**:
   - Try deploying from a different network connection
   - Use the Deno Deploy web dashboard for manual deployment
   - Connect a GitHub repository for automatic deployments

2. **Email verification not working**:
   - Verify your domain is set up correctly in Resend
   - Check that `RESEND_API_KEY` and `BASE_URL` environment variables are set
   - Ensure API key has email sending permissions (not just domain access)

3. **Environment not detected as production**:
   - Set `DENO_ENV=production` in environment variables
   - Check that `DENO_DEPLOYMENT_ID` is automatically set by Deno Deploy

4. **Logs not appearing in production**:
   - Production uses ERROR level logging by default
   - Set `LOG_LEVEL=INFO` to see more detailed logs
   - Use `LOG_LEVEL=DEBUG` for full debugging (not recommended for production)

5. **Admin endpoints returning 401 Unauthorized**:
   - Verify `ADMIN_KEY` environment variable is set in Deno Deploy
   - Ensure you're sending the Authorization header: `Bearer your_admin_key`
   - Generate a new admin key if needed: `openssl rand -hex 32`

6. **KV operations failing**:
   - Check Deno Deploy KV service is enabled for your project
   - Verify the application has `--unstable-kv` permission
   - Use admin stats endpoint to verify KV store accessibility

### Environment Variables

**Required for production:**

```bash
# Email Service Configuration
RESEND_API_KEY=your_resend_api_key    # For email verification (get from resend.com)
BASE_URL=https://your-domain.deno.dev # Your deployment URL for email links

# Environment Detection
DENO_ENV=production                   # Forces production mode (optional, auto-detected)

# Admin Access (Optional)
ADMIN_KEY=your_secure_random_key      # For KV database management via HTTP
```

**Optional logging configuration:**

```bash
# Logging Configuration (Environment-Aware Defaults)
LOG_LEVEL=DEBUG|INFO|WARN|ERROR       # Default: ERROR in prod, DEBUG in dev
ENABLE_STRUCTURED_LOGS=true|false     # Default: false in prod, true in dev  
ENABLE_CLIENT_LOGS=true|false         # Default: false in prod, true in dev
DEBUG=true|false                      # Legacy debug mode support
```

#### Logging Behavior

- **Production**: Minimal logging (ERROR level only) for optimal performance and security
- **Development**: Verbose logging (DEBUG level) with detailed debugging information
- **Override**: Set `LOG_LEVEL=INFO` in production for more detailed logs when troubleshooting
- **Structured logs**: Enable JSON-formatted logs for log aggregation and monitoring systems
- **Client logs**: Control browser console logging independently from server logging
- **Zero overhead**: Disabled logging levels have no performance impact

#### Email Service Configuration

The email system uses environment-aware defaults:
- **Demo mode**: When `RESEND_API_KEY` is not set, logs verification links to console
- **Production**: Sends real emails through Resend API using your verified domain
- **Development**: Uses real email API if key is provided, otherwise falls back to demo mode

## 🤝 Contributing

Contributions are welcome! The modular architecture makes it easy to:

- Add new applications to `apps/`
- Enhance shared libraries in `libs/`
- Improve existing applications
- Add new shared utilities

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🎖️ Acknowledgments

- **Deno** for providing a secure and modern runtime environment
- **HTMX** for simplifying UI interactions without complex JavaScript
- **Effection** for structured concurrency and safe resource management
- **WebAuthn** for passwordless authentication
- **Resend** for reliable email delivery

## 📋 Quick Reference

### Essential Commands

```bash
# Development
cd apps/hangman
deno task dev                    # Start development server
deno task deploy                 # Deploy to Deno Deploy

# Database Management (Development)
deno run --allow-env --unstable-kv scripts/kv-utils.ts stats

# Database Management (Production)
curl -H "Authorization: Bearer $ADMIN_KEY" \
     https://your-hangman.dev/admin/kv/stats
```

### Environment Variables Checklist

**Required:**
- ✅ `RESEND_API_KEY` - Your Resend API key
- ✅ `BASE_URL` - Your deployment URL
- ✅ `ADMIN_KEY` - For database management (optional)

**Optional:**
- `DENO_ENV=production` - Force production mode
- `LOG_LEVEL=ERROR|WARN|INFO|DEBUG` - Control logging verbosity

---

**Crafted with ❤️ by [⊣˚∆˚⊢](https://srdjan.github.io)**